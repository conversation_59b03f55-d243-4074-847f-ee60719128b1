{"version": 3, "names": ["registerEventHandler", "unregisterEventHandler", "shouldBeUseWeb", "SHOULD_BE_USE_WEB", "jsListener", "eventName", "handler", "evt", "nativeEvent", "WorkletEventHandlerNative", "viewTags", "registrations", "constructor", "worklet", "eventNames", "Set", "Map", "updateEventHandler", "newWorklet", "newEvents", "for<PERSON>ach", "registrationIDs", "id", "Array", "from", "tag", "newRegistrations", "map", "set", "registerForEvents", "viewTag", "fallbackEventName", "add", "length", "newRegistration", "unregisterFromEvents", "delete", "get", "WorkletEventHandlerWeb", "listeners", "setupWebListeners", "_viewTag", "_fallbackEventName", "WorkletEventHandler"], "sourceRoot": "../../src", "sources": ["WorkletEventHandler.ts"], "mappings": "AAAA,YAAY;;AAGZ,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,WAAQ;AAMrE,SAASC,cAAc,QAAQ,sBAAmB;AAElD,MAAMC,iBAAiB,GAAGD,cAAc,CAAC,CAAC;AAI1C;AACA;AACA;AACA,SAASE,UAAUA,CACjBC,SAAiB,EACjBC,OAAgD,EAChD;EACA,OAAQC,GAAmB,IAAK;IAC9BD,OAAO,CAAC;MAAE,GAAGC,GAAG,CAACC,WAAW;MAAEH;IAAU,CAA2B,CAAC;EACtE,CAAC;AACH;AAEA,MAAMI,yBAAyB,CAE/B;EAGE,CAACC,QAAQ;EACT,CAACC,aAAa,CAAwB,CAAC;EACvCC,WAAWA,CACTC,OAAgD,EAChDC,UAAoB,EACpB;IACA,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC,CAACJ,QAAQ,GAAG,IAAIK,GAAG,CAAS,CAAC;IAClC,IAAI,CAAC,CAACJ,aAAa,GAAG,IAAIK,GAAG,CAAmB,CAAC;EACnD;EAEAC,kBAAkBA,CAChBC,UAAmD,EACnDC,SAAmB,EACb;IACN;IACA,IAAI,CAACN,OAAO,GAAGK,UAAU;IACzB,IAAI,CAACJ,UAAU,GAAGK,SAAS;;IAE3B;IACA,IAAI,CAAC,CAACR,aAAa,CAACS,OAAO,CAAEC,eAAe,IAAK;MAC/CA,eAAe,CAACD,OAAO,CAAEE,EAAE,IAAKrB,sBAAsB,CAACqB,EAAE,CAAC,CAAC;MAC3D;IACF,CAAC,CAAC;;IAEF;IACAC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC,CAACd,QAAQ,CAAC,CAACU,OAAO,CAAEK,GAAG,IAAK;MAC1C,MAAMC,gBAAgB,GAAG,IAAI,CAACZ,UAAU,CAACa,GAAG,CAAEtB,SAAS,IACrDL,oBAAoB,CAAC,IAAI,CAACa,OAAO,EAAER,SAAS,EAAEoB,GAAG,CACnD,CAAC;MACD,IAAI,CAAC,CAACd,aAAa,CAACiB,GAAG,CAACH,GAAG,EAAEC,gBAAgB,CAAC;IAChD,CAAC,CAAC;EACJ;EAEAG,iBAAiBA,CAACC,OAAe,EAAEC,iBAA0B,EAAQ;IACnE,IAAI,CAAC,CAACrB,QAAQ,CAACsB,GAAG,CAACF,OAAO,CAAC;IAE3B,MAAMJ,gBAAgB,GAAG,IAAI,CAACZ,UAAU,CAACa,GAAG,CAAEtB,SAAS,IACrDL,oBAAoB,CAAC,IAAI,CAACa,OAAO,EAAER,SAAS,EAAEyB,OAAO,CACvD,CAAC;IACD,IAAI,CAAC,CAACnB,aAAa,CAACiB,GAAG,CAACE,OAAO,EAAEJ,gBAAgB,CAAC;IAElD,IAAI,IAAI,CAACZ,UAAU,CAACmB,MAAM,KAAK,CAAC,IAAIF,iBAAiB,EAAE;MACrD,MAAMG,eAAe,GAAGlC,oBAAoB,CAC1C,IAAI,CAACa,OAAO,EACZkB,iBAAiB,EACjBD,OACF,CAAC;MACD,IAAI,CAAC,CAACnB,aAAa,CAACiB,GAAG,CAACE,OAAO,EAAE,CAACI,eAAe,CAAC,CAAC;IACrD;EACF;EAEAC,oBAAoBA,CAACL,OAAe,EAAQ;IAC1C,IAAI,CAAC,CAACpB,QAAQ,CAAC0B,MAAM,CAACN,OAAO,CAAC;IAC9B,IAAI,CAAC,CAACnB,aAAa,CAAC0B,GAAG,CAACP,OAAO,CAAC,EAAEV,OAAO,CAAEE,EAAE,IAAK;MAChDrB,sBAAsB,CAACqB,EAAE,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAAC,CAACX,aAAa,CAACyB,MAAM,CAACN,OAAO,CAAC;EACrC;AACF;AAEA,MAAMQ,sBAAsB,CAE5B;EAQE1B,WAAWA,CACTC,OAAgD,EAChDC,UAAoB,GAAG,EAAE,EACzB;IACA,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACyB,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;EAEAA,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACD,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACzB,UAAU,CAACM,OAAO,CAAEf,SAAS,IAAK;MACrC,IAAI,CAACkC,SAAS,CAAClC,SAAS,CAAC,GAAGD,UAAU,CAACC,SAAS,EAAE,IAAI,CAACQ,OAAO,CAAC;IACjE,CAAC,CAAC;EACJ;EAEAI,kBAAkBA,CAChBC,UAAmD,EACnDC,SAAmB,EACb;IACN;IACA,IAAI,CAACN,OAAO,GAAGK,UAAU;IACzB,IAAI,CAACJ,UAAU,GAAGK,SAAS;IAC3B,IAAI,CAACqB,iBAAiB,CAAC,CAAC;EAC1B;EAEAX,iBAAiBA,CAACY,QAAgB,EAAEC,kBAA2B,EAAQ;IACrE;EAAA;EAGFP,oBAAoBA,CAACM,QAAgB,EAAQ;IAC3C;EAAA;AAEJ;AAEA,OAAO,MAAME,mBAAmB,GAAGxC,iBAAiB,GAChDmC,sBAAsB,GACtB7B,yBAAyB", "ignoreList": []}