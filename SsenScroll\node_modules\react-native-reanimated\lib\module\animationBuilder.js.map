{"version": 3, "names": ["logger", "mockTargetValues", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight", "targetGlobalOriginX", "targetGlobalOriginY", "targetBorderRadius", "windowWidth", "windowHeight", "currentOriginX", "currentOriginY", "currentWidth", "currentHeight", "currentGlobalOriginX", "currentGlobalOriginY", "currentBorderRadius", "getCommonProperties", "layoutStyle", "componentStyle", "componentStyleFlat", "Array", "isArray", "flat", "filter", "Boolean", "map", "style", "initial", "value", "componentStylesKeys", "flatMap", "Object", "keys", "commonKeys", "key", "includes", "maybeReportOverwrittenProperties", "layoutAnimationStyle", "displayName", "commonProperties", "length", "warn", "join", "maybeBuild", "layoutAnimationOrBuilder", "isAnimationBuilder", "build", "animationFactory", "__DEV__", "layoutAnimation", "animations"], "sourceRoot": "../../src", "sources": ["animationBuilder.tsx"], "mappings": "AAAA,YAAY;;AAQZ,SAASA,MAAM,QAAQ,mBAAU;AAEjC,MAAMC,gBAAwC,GAAG;EAC/CC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,CAAC;EAChBC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,CAAC;EACfC,mBAAmB,EAAE,CAAC;EACtBC,mBAAmB,EAAE,CAAC;EACtBC,kBAAkB,EAAE,CAAC;EACrBC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,CAAC;EACfC,cAAc,EAAE,CAAC;EACjBC,cAAc,EAAE,CAAC;EACjBC,YAAY,EAAE,CAAC;EACfC,aAAa,EAAE,CAAC;EAChBC,oBAAoB,EAAE,CAAC;EACvBC,oBAAoB,EAAE,CAAC;EACvBC,mBAAmB,EAAE;AACvB,CAAC;AAED,SAASC,mBAAmBA,CAC1BC,WAAuB,EACvBC,cAAuC,EACvC;EACA,IAAIC,kBAAkB,GAAGC,KAAK,CAACC,OAAO,CAACH,cAAc,CAAC,GAClDA,cAAc,CAACI,IAAI,CAAC,CAAC,GACrB,CAACJ,cAAc,CAAC;EAEpBC,kBAAkB,GAAGA,kBAAkB,CAACI,MAAM,CAACC,OAAO,CAAC;EAEvDL,kBAAkB,GAAGA,kBAAkB,CAACM,GAAG,CAAEC,KAAK,IAChD,SAAS,IAAIA,KAAK,GACdA,KAAK,CAACC,OAAO,CAACC,KAAK,CAAC;EAAA,EACpBF,KACN,CAAC;EAED,MAAMG,mBAAmB,GAAGV,kBAAkB,CAACW,OAAO,CAAEJ,KAAK,IAC3DK,MAAM,CAACC,IAAI,CAACN,KAAK,CACnB,CAAC;EAED,MAAMO,UAAU,GAAGF,MAAM,CAACC,IAAI,CAACf,WAAW,CAAC,CAACM,MAAM,CAAEW,GAAG,IACrDL,mBAAmB,CAACM,QAAQ,CAACD,GAAG,CAClC,CAAC;EAED,OAAOD,UAAU;AACnB;AAEA,SAASG,gCAAgCA,CACvCC,oBAAgC,EAChCX,KAA8B,EAC9BY,WAAmB,EACnB;EACA,MAAMC,gBAAgB,GAAGvB,mBAAmB,CAACqB,oBAAoB,EAAEX,KAAK,CAAC;EAEzE,IAAIa,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;IAC/B1C,MAAM,CAAC2C,IAAI,CACT,GACEF,gBAAgB,CAACC,MAAM,KAAK,CAAC,GAAG,UAAU,GAAG,YAAY,KACtDD,gBAAgB,CAACG,IAAI,CACxB,IACF,CAAC,QAAQJ,WAAW,4IACtB,CAAC;EACH;AACF;AAEA,OAAO,SAASK,UAAUA,CACxBC,wBAGY,EACZlB,KAA0C,EAC1CY,WAAmB,EACiB;EACpC,MAAMO,kBAAkB,GACtBjB,KAAmE,IAEnE,OAAO,IAAIgB,wBAAwB,IACnC,OAAOA,wBAAwB,CAACE,KAAK,KAAK,UAAU;EAEtD,IAAID,kBAAkB,CAACD,wBAAwB,CAAC,EAAE;IAChD,MAAMG,gBAAgB,GAAGH,wBAAwB,CAACE,KAAK,CAAC,CAAC;IAEzD,IAAIE,OAAO,IAAItB,KAAK,EAAE;MACpB,MAAMuB,eAAe,GAAGF,gBAAgB,CAAChD,gBAAgB,CAAC;MAC1DqC,gCAAgC,CAC9Ba,eAAe,CAACC,UAAU,EAC1BxB,KAAK,EACLY,WACF,CAAC;IACH;IAEA,OAAOS,gBAAgB;EACzB,CAAC,MAAM;IACL,OAAOH,wBAAwB;EACjC;AACF", "ignoreList": []}