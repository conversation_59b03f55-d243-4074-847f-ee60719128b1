{"version": 3, "names": ["ReanimatedError", "defineAnimation", "getReduceMotionForAnimation", "rigidDecay", "rubberBandDecay", "isValidRubberBandConfig", "validateConfig", "config", "clamp", "Array", "isArray", "length", "velocityFactor", "rubberBandEffect", "<PERSON><PERSON><PERSON><PERSON>", "userConfig", "callback", "deceleration", "velocity", "rubberBandFactor", "Object", "keys", "for<PERSON>ach", "key", "decay", "animation", "now", "onStart", "value", "initialVelocity", "current", "lastTimestamp", "startTimestamp", "reduceMotion", "onFrame", "undefined"], "sourceRoot": "../../../../src", "sources": ["animation/decay/decay.ts"], "mappings": "AAAA,YAAY;;AAMZ,SAASA,eAAe,QAAQ,iBAAc;AAC9C,SAASC,eAAe,EAAEC,2BAA2B,QAAQ,YAAS;AACtE,SAASC,UAAU,QAAQ,iBAAc;AACzC,SAASC,eAAe,QAAQ,sBAAmB;AAOnD,SAASC,uBAAuB,QAAQ,YAAS;;AAIjD;;AAMA,SAASC,cAAcA,CAACC,MAA0B,EAAQ;EACxD,SAAS;;EACT,IAAIA,MAAM,CAACC,KAAK,EAAE;IAChB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,MAAM,CAACC,KAAK,CAAC,EAAE;MAChC,MAAM,IAAIR,eAAe,CACvB,4CAA4C,OAAOO,MAAM,CAACC,KAAK,GACjE,CAAC;IACH;IACA,IAAID,MAAM,CAACC,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;MAC7B,MAAM,IAAIX,eAAe,CACvB,qDACEO,MAAM,CAACC,KAAK,CAACG,MAAM,GAEvB,CAAC;IACH;EACF;EACA,IAAIJ,MAAM,CAACK,cAAc,IAAI,CAAC,EAAE;IAC9B,MAAM,IAAIZ,eAAe,CACvB,2DAA2DO,MAAM,CAACK,cAAc,GAClF,CAAC;EACH;EACA,IAAIL,MAAM,CAACM,gBAAgB,IAAI,CAACN,MAAM,CAACC,KAAK,EAAE;IAC5C,MAAM,IAAIR,eAAe,CACvB,iEACF,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMc,SAAS,GAAG,SAAAA,CACvBC,UAAuB,EACvBC,QAA4B,EACD;EAC3B,SAAS;;EAET,OAAOf,eAAe,CAAiB,CAAC,EAAE,MAAM;IAC9C,SAAS;;IACT,MAAMM,MAA0B,GAAG;MACjCU,YAAY,EAAE,KAAK;MACnBL,cAAc,EAAE,CAAC;MACjBM,QAAQ,EAAE,CAAC;MACXC,gBAAgB,EAAE;IACpB,CAAC;IACD,IAAIJ,UAAU,EAAE;MACdK,MAAM,CAACC,IAAI,CAACN,UAAU,CAAC,CAACO,OAAO,CAC5BC,GAAG,IACAhB,MAAM,CAASgB,GAAG,CAAC,GAAGR,UAAU,CAACQ,GAAG,CAC1C,CAAC;IACH;IAEA,MAAMC,KAA+D,GACnEnB,uBAAuB,CAACE,MAAM,CAAC,GAC3B,CAACkB,SAAS,EAAEC,GAAG,KAAKtB,eAAe,CAACqB,SAAS,EAAEC,GAAG,EAAEnB,MAAM,CAAC,GAC3D,CAACkB,SAAS,EAAEC,GAAG,KAAKvB,UAAU,CAACsB,SAAS,EAAEC,GAAG,EAAEnB,MAAM,CAAC;IAE5D,SAASoB,OAAOA,CACdF,SAAyB,EACzBG,KAAa,EACbF,GAAc,EACR;MACN,MAAMG,eAAe,GAAGtB,MAAM,CAACW,QAAQ;MACvCO,SAAS,CAACK,OAAO,GAAGF,KAAK;MACzBH,SAAS,CAACM,aAAa,GAAGL,GAAG;MAC7BD,SAAS,CAACO,cAAc,GAAGN,GAAG;MAC9BD,SAAS,CAACI,eAAe,GAAGA,eAAe;MAC3CJ,SAAS,CAACP,QAAQ,GAAGW,eAAe;MAEpCvB,cAAc,CAACC,MAAM,CAAC;MAEtB,IAAIkB,SAAS,CAACQ,YAAY,IAAI1B,MAAM,CAACC,KAAK,EAAE;QAC1C,IAAIoB,KAAK,GAAGrB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;UAC3BiB,SAAS,CAACK,OAAO,GAAGvB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;QACrC,CAAC,MAAM,IAAIoB,KAAK,GAAGrB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;UAClCiB,SAAS,CAACK,OAAO,GAAGvB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;QACrC;MACF;IACF;;IAEA;IACA;IACA;IACA,OAAO;MACL0B,OAAO,EAAEV,KAAK;MACdG,OAAO;MACPX,QAAQ;MACRE,QAAQ,EAAEX,MAAM,CAACW,QAAQ,IAAI,CAAC;MAC9BW,eAAe,EAAE,CAAC;MAClBC,OAAO,EAAEK,SAAS;MAClBJ,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC;MACjBC,YAAY,EAAE/B,2BAA2B,CAACK,MAAM,CAAC0B,YAAY;IAC/D,CAAC;EACH,CAAC,CAAC;AACJ,CAA6B", "ignoreList": []}