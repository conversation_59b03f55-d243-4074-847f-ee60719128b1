{"version": 3, "names": ["defineAnimation", "getReduceMotionForAnimation", "withRepeat", "_nextAnimation", "numberOfReps", "reverse", "callback", "reduceMotion", "nextAnimation", "repeat", "animation", "now", "finished", "onFrame", "current", "reps", "startValue", "toValue", "onStart", "previousAnimation", "rep<PERSON><PERSON><PERSON>", "value", "undefined", "isHigherOrder"], "sourceRoot": "../../../src", "sources": ["animation/repeat.ts"], "mappings": "AAAA,YAAY;;AAUZ,SAASA,eAAe,EAAEC,2BAA2B,QAAQ,WAAQ;;AAErE;;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAG,SAAAA,CACxBC,cAA6B,EAC7BC,YAAY,GAAG,CAAC,EAChBC,OAAO,GAAG,KAAK,EACfC,QAA4B,EAC5BC,YAA2B,EACC;EAC5B,SAAS;;EAET,OAAOP,eAAe,CACpBG,cAAc,EACd,MAAuB;IACrB,SAAS;;IAET,MAAMK,aAAa,GACjB,OAAOL,cAAc,KAAK,UAAU,GAChCA,cAAc,CAAC,CAAC,GAChBA,cAAc;IAEpB,SAASM,MAAMA,CAACC,SAA0B,EAAEC,GAAc,EAAW;MACnE,MAAMC,QAAQ,GAAGJ,aAAa,CAACK,OAAO,CAACL,aAAa,EAAEG,GAAG,CAAC;MAC1DD,SAAS,CAACI,OAAO,GAAGN,aAAa,CAACM,OAAO;MACzC,IAAIF,QAAQ,EAAE;QACZF,SAAS,CAACK,IAAI,IAAI,CAAC;QACnB;QACA;QACA,IAAIP,aAAa,CAACF,QAAQ,EAAE;UAC1BE,aAAa,CAACF,QAAQ,CAAC,IAAI,CAAC,gBAAgBI,SAAS,CAACI,OAAO,CAAC;QAChE;QACA,IACEJ,SAAS,CAACH,YAAY,IACrBH,YAAY,GAAG,CAAC,IAAIM,SAAS,CAACK,IAAI,IAAIX,YAAa,EACpD;UACA,OAAO,IAAI;QACb;QAEA,MAAMY,UAAU,GAAGX,OAAO,GACrBG,aAAa,CAACM,OAAO,GACtBJ,SAAS,CAACM,UAAU;QACxB,IAAIX,OAAO,EAAE;UACXG,aAAa,CAACS,OAAO,GAAGP,SAAS,CAACM,UAAU;UAC5CN,SAAS,CAACM,UAAU,GAAGA,UAAU;QACnC;QACAR,aAAa,CAACU,OAAO,CACnBV,aAAa,EACbQ,UAAU,EACVL,GAAG,EACHH,aAAa,CAACW,iBAChB,CAAC;QACD,OAAO,KAAK;MACd;MACA,OAAO,KAAK;IACd;IAEA,MAAMC,WAAW,GAAIR,QAAkB,IAAW;MAChD,IAAIN,QAAQ,EAAE;QACZA,QAAQ,CAACM,QAAQ,CAAC;MACpB;MACA;MACA,IAAI,CAACA,QAAQ,IAAIJ,aAAa,CAACF,QAAQ,EAAE;QACvCE,aAAa,CAACF,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC;MAC9C;IACF,CAAC;IAED,SAASY,OAAOA,CACdR,SAA0B,EAC1BW,KAAsB,EACtBV,GAAc,EACdQ,iBAAwC,EAClC;MACNT,SAAS,CAACM,UAAU,GAAGK,KAAK;MAC5BX,SAAS,CAACK,IAAI,GAAG,CAAC;;MAElB;MACA;MACA,IAAIP,aAAa,CAACD,YAAY,KAAKe,SAAS,EAAE;QAC5Cd,aAAa,CAACD,YAAY,GAAGG,SAAS,CAACH,YAAY;MACrD;;MAEA;MACA;MACA,IACEG,SAAS,CAACH,YAAY,IACtBF,OAAO,KACND,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAG,CAAC,KAAK,CAAC,CAAC,EAC7C;QACAM,SAAS,CAACI,OAAO,GAAGJ,SAAS,CAACM,UAAU;QACxCN,SAAS,CAACG,OAAO,GAAG,MAAM,IAAI;MAChC,CAAC,MAAM;QACLL,aAAa,CAACU,OAAO,CAACV,aAAa,EAAEa,KAAK,EAAEV,GAAG,EAAEQ,iBAAiB,CAAC;MACrE;IACF;IAEA,OAAO;MACLI,aAAa,EAAE,IAAI;MACnBV,OAAO,EAAEJ,MAAM;MACfS,OAAO;MACPH,IAAI,EAAE,CAAC;MACPD,OAAO,EAAEN,aAAa,CAACM,OAAO;MAC9BR,QAAQ,EAAEc,WAAW;MACrBJ,UAAU,EAAE,CAAC;MACbT,YAAY,EAAEN,2BAA2B,CAACM,YAAY;IACxD,CAAC;EACH,CACF,CAAC;AACH,CAAmB", "ignoreList": []}