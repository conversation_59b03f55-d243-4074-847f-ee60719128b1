{"version": 3, "names": ["Easing", "assertEasingIsWorklet", "defineAnimation", "getReduceMotionForAnimation", "withTiming", "toValue", "userConfig", "callback", "__DEV__", "easing", "config", "duration", "inOut", "quad", "Object", "keys", "for<PERSON>ach", "key", "timing", "animation", "now", "startTime", "startValue", "runtime", "current", "progress", "onStart", "value", "previousAnimation", "type", "factory", "onFrame", "reduceMotion"], "sourceRoot": "../../../src", "sources": ["animation/timing.ts"], "mappings": "AAAA,YAAY;;AAUZ,SAASA,MAAM,QAAQ,cAAW;AAClC,SACEC,qBAAqB,EACrBC,eAAe,EACfC,2BAA2B,QACtB,WAAQ;;AAEf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAyBA;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAG,SAAAA,CACxBC,OAAwB,EACxBC,UAAyB,EACzBC,QAA4B,EACA;EAC5B,SAAS;;EAET,IAAIC,OAAO,IAAIF,UAAU,EAAEG,MAAM,EAAE;IACjCR,qBAAqB,CAACK,UAAU,CAACG,MAAM,CAAC;EAC1C;EAEA,OAAOP,eAAe,CAAkBG,OAAO,EAAE,MAAM;IACrD,SAAS;;IACT,MAAMK,MAAoD,GAAG;MAC3DC,QAAQ,EAAE,GAAG;MACbF,MAAM,EAAET,MAAM,CAACY,KAAK,CAACZ,MAAM,CAACa,IAAI;IAClC,CAAC;IACD,IAAIP,UAAU,EAAE;MACdQ,MAAM,CAACC,IAAI,CAACT,UAAU,CAAC,CAACU,OAAO,CAC5BC,GAAG,IACAP,MAAM,CAASO,GAAG,CAAC,GAAGX,UAAU,CAACW,GAAG,CAC1C,CAAC;IACH;IAEA,SAASC,MAAMA,CAACC,SAA+B,EAAEC,GAAc,EAAW;MACxE;MACA,MAAM;QAAEf,OAAO;QAAEgB,SAAS;QAAEC;MAAW,CAAC,GAAGH,SAAS;MACpD,MAAMI,OAAO,GAAGH,GAAG,GAAGC,SAAS;MAE/B,IAAIE,OAAO,IAAIb,MAAM,CAACC,QAAQ,EAAE;QAC9B;QACAQ,SAAS,CAACE,SAAS,GAAG,CAAC;QACvBF,SAAS,CAACK,OAAO,GAAGnB,OAAO;QAC3B,OAAO,IAAI;MACb;MACA,MAAMoB,QAAQ,GAAGN,SAAS,CAACV,MAAM,CAACc,OAAO,GAAGb,MAAM,CAACC,QAAQ,CAAC;MAC5DQ,SAAS,CAACK,OAAO,GACdF,UAAU,GAAc,CAACjB,OAAO,GAAIiB,UAAqB,IAAIG,QAAQ;MACxE,OAAO,KAAK;IACd;IAEA,SAASC,OAAOA,CACdP,SAA0B,EAC1BQ,KAAa,EACbP,GAAc,EACdQ,iBAA6C,EACvC;MACN,IACEA,iBAAiB,IAChBA,iBAAiB,CAAqBC,IAAI,KAAK,QAAQ,IACvDD,iBAAiB,CAAqBvB,OAAO,KAAKA,OAAO,IACzDuB,iBAAiB,CAAqBP,SAAS,EAChD;QACA;QACA;QACA;QACAF,SAAS,CAACE,SAAS,GAAIO,iBAAiB,CAAqBP,SAAS;QACtEF,SAAS,CAACG,UAAU,GAClBM,iBAAiB,CACjBN,UAAU;MACd,CAAC,MAAM;QACLH,SAAS,CAACE,SAAS,GAAGD,GAAG;QACzBD,SAAS,CAACG,UAAU,GAAGK,KAAK;MAC9B;MACAR,SAAS,CAACK,OAAO,GAAGG,KAAK;MACzB,IAAI,OAAOjB,MAAM,CAACD,MAAM,KAAK,QAAQ,EAAE;QACrCU,SAAS,CAACV,MAAM,GAAGC,MAAM,CAACD,MAAM,CAACqB,OAAO,CAAC,CAAC;MAC5C,CAAC,MAAM;QACLX,SAAS,CAACV,MAAM,GAAGC,MAAM,CAACD,MAAM;MAClC;IACF;IAEA,OAAO;MACLoB,IAAI,EAAE,QAAQ;MACdE,OAAO,EAAEb,MAAM;MACfQ,OAAO,EAAEA,OAA+D;MACxED,QAAQ,EAAE,CAAC;MACXpB,OAAO;MACPiB,UAAU,EAAE,CAAC;MACbD,SAAS,EAAE,CAAC;MACZZ,MAAM,EAAEA,CAAA,KAAM,CAAC;MACfe,OAAO,EAAEnB,OAAO;MAChBE,QAAQ;MACRyB,YAAY,EAAE7B,2BAA2B,CAACG,UAAU,EAAE0B,YAAY;IACpE,CAAC;EACH,CAAC,CAAC;AACJ,CAAmB", "ignoreList": []}