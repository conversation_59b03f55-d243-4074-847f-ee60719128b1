{"version": 3, "names": ["useEffect", "useRef", "KeyboardState", "makeMutable", "subscribeForKeyboardEvents", "unsubscribeFromKeyboardEvents", "useAnimatedKeyboard", "options", "isStatusBarTranslucentAndroid", "undefined", "isNavigationBarTranslucentAndroid", "ref", "listenerId", "isSubscribed", "current", "keyboardEventData", "state", "UNKNOWN", "height", "value"], "sourceRoot": "../../../src", "sources": ["hook/useAnimatedKeyboard.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAMzC,SAASC,aAAa,QAAQ,mBAAgB;AAC9C,SACEC,WAAW,EACXC,0BAA0B,EAC1BC,6BAA6B,QACxB,YAAS;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CACjCC,OAAgC,GAAG;EACjCC,6BAA6B,EAAEC,SAAS;EACxCC,iCAAiC,EAAED;AACrC,CAAC,EACqB;EACtB,MAAME,GAAG,GAAGV,MAAM,CAA8B,IAAI,CAAC;EACrD,MAAMW,UAAU,GAAGX,MAAM,CAAS,CAAC,CAAC,CAAC;EACrC,MAAMY,YAAY,GAAGZ,MAAM,CAAU,KAAK,CAAC;EAE3C,IAAIU,GAAG,CAACG,OAAO,KAAK,IAAI,EAAE;IACxB,MAAMC,iBAAuC,GAAG;MAC9CC,KAAK,EAAEb,WAAW,CAAgBD,aAAa,CAACe,OAAO,CAAC;MACxDC,MAAM,EAAEf,WAAW,CAAC,CAAC;IACvB,CAAC;IACDS,UAAU,CAACE,OAAO,GAAGV,0BAA0B,CAAC,CAACY,KAAK,EAAEE,MAAM,KAAK;MACjE,SAAS;;MACTH,iBAAiB,CAACC,KAAK,CAACG,KAAK,GAAGH,KAAK;MACrCD,iBAAiB,CAACG,MAAM,CAACC,KAAK,GAAGD,MAAM;IACzC,CAAC,EAAEX,OAAO,CAAC;IACXI,GAAG,CAACG,OAAO,GAAGC,iBAAiB;IAC/BF,YAAY,CAACC,OAAO,GAAG,IAAI;EAC7B;EACAd,SAAS,CAAC,MAAM;IACd,IAAIa,YAAY,CAACC,OAAO,KAAK,KAAK,IAAIH,GAAG,CAACG,OAAO,KAAK,IAAI,EAAE;MAC1D,MAAMC,iBAAiB,GAAGJ,GAAG,CAACG,OAAO;MACrC;MACAF,UAAU,CAACE,OAAO,GAAGV,0BAA0B,CAAC,CAACY,KAAK,EAAEE,MAAM,KAAK;QACjE,SAAS;;QACTH,iBAAiB,CAACC,KAAK,CAACG,KAAK,GAAGH,KAAK;QACrCD,iBAAiB,CAACG,MAAM,CAACC,KAAK,GAAGD,MAAM;MACzC,CAAC,EAAEX,OAAO,CAAC;MACXM,YAAY,CAACC,OAAO,GAAG,IAAI;IAC7B;IACA,OAAO,MAAM;MACXT,6BAA6B,CAACO,UAAU,CAACE,OAAO,CAAC;MACjDD,YAAY,CAACC,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOH,GAAG,CAACG,OAAO;AACpB", "ignoreList": []}