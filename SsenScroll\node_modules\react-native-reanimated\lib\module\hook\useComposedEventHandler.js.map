{"version": 3, "names": ["WorkletEventHandler", "useEvent", "useHandler", "useComposedEventHandler", "handlers", "workletsRecord", "composedEventNames", "Set", "workletsMap", "filter", "h", "for<PERSON>ach", "handler", "workletEventHandler", "eventNames", "eventName", "add", "push", "worklet", "handler<PERSON>ame", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "Array", "from"], "sourceRoot": "../../../src", "sources": ["hook/useComposedEventHandler.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,mBAAmB,QAAQ,2BAAwB;AAG5D,SAASC,QAAQ,QAAQ,eAAY;AACrC,SAASC,UAAU,QAAQ,iBAAc;;AAUzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAQA,OAAO,SAASC,uBAAuBA,CAGrCC,QAA0D,EAAE;EAC5D;EACA,MAAMC,cAA+C,GAAG,CAAC,CAAC;EAC1D;EACA,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,CAAS,CAAC;EAC5C;EACA,MAAMC,WAEL,GAAG,CAAC,CAAC;EAENJ,QAAQ,CACLK,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAK,IAAI,CAAC,CACzBC,OAAO,CAAEC,OAAO,IAAK;IACpB;IACA,MAAM;MAAEC;IAAoB,CAAC,GAC3BD,OAAmD;IACrD,IAAIC,mBAAmB,YAAYb,mBAAmB,EAAE;MACtDa,mBAAmB,CAACC,UAAU,CAACH,OAAO,CAAEI,SAAS,IAAK;QACpDT,kBAAkB,CAACU,GAAG,CAACD,SAAS,CAAC;QAEjC,IAAIP,WAAW,CAACO,SAAS,CAAC,EAAE;UAC1BP,WAAW,CAACO,SAAS,CAAC,CAACE,IAAI,CAACJ,mBAAmB,CAACK,OAAO,CAAC;QAC1D,CAAC,MAAM;UACLV,WAAW,CAACO,SAAS,CAAC,GAAG,CAACF,mBAAmB,CAACK,OAAO,CAAC;QACxD;QAEA,MAAMC,WAAW,GAAGJ,SAAS,GAAG,GAAGP,WAAW,CAACO,SAAS,CAAC,CAACK,MAAM,EAAE;QAClEf,cAAc,CAACc,WAAW,CAAC,GACzBN,mBAAmB,CAACK,OAA0B;MAClD,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEJ,MAAM;IAAEG;EAAqB,CAAC,GAAGnB,UAAU,CAACG,cAAc,CAAC;EAE3D,OAAOJ,QAAQ,CACZqB,KAAK,IAAK;IACT,SAAS;;IACT,IAAId,WAAW,CAACc,KAAK,CAACP,SAAS,CAAC,EAAE;MAChCP,WAAW,CAACc,KAAK,CAACP,SAAS,CAAC,CAACJ,OAAO,CAAEO,OAAO,IAAKA,OAAO,CAACI,KAAK,CAAC,CAAC;IACnE;EACF,CAAC,EACDC,KAAK,CAACC,IAAI,CAAClB,kBAAkB,CAAC,EAC9Be,oBACF,CAAC;AACH", "ignoreList": []}