{"version": 3, "names": ["useRef", "WorkletEventHandler", "useEvent", "handler", "eventNames", "rebuild", "initRef", "current", "workletEventHandler", "updateEventHandler"], "sourceRoot": "../../../src", "sources": ["hook/useEvent.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,MAAM,QAAQ,OAAO;AAE9B,SAASC,mBAAmB,QAAQ,2BAAwB;;AAG5D;;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAUA,OAAO,SAASC,QAAQA,CACtBC,OAAmE,EACnEC,UAAoB,GAAG,EAAE,EACzBC,OAAO,GAAG,KAAK,EACc;EAC7B,MAAMC,OAAO,GAAGN,MAAM,CAA8B,IAAK,CAAC;EAC1D,IAAIM,OAAO,CAACC,OAAO,KAAK,IAAI,EAAE;IAC5B,MAAMC,mBAAmB,GAAG,IAAIP,mBAAmB,CACjDE,OAAO,EACPC,UACF,CAAC;IACDE,OAAO,CAACC,OAAO,GAAG;MAAEC;IAAoB,CAAC;EAC3C,CAAC,MAAM,IAAIH,OAAO,EAAE;IAClB,MAAMG,mBAAmB,GAAGF,OAAO,CAACC,OAAO,CAACC,mBAAmB;IAC/DA,mBAAmB,CAACC,kBAAkB,CAACN,OAAO,EAAEC,UAAU,CAAC;IAC3DE,OAAO,CAACC,OAAO,GAAG;MAAEC;IAAoB,CAAC;EAC3C;EAEA,OAAOF,OAAO,CAACC,OAAO;AACxB", "ignoreList": []}