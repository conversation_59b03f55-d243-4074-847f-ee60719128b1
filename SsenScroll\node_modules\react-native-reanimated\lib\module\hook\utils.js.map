{"version": 3, "names": ["ReanimatedError", "buildWorkletsHash", "worklets", "Object", "values", "reduce", "acc", "worklet", "__workletHash", "toString", "buildDependencies", "dependencies", "handlers", "handlersList", "filter", "handler", "undefined", "map", "workletHash", "closure", "__closure", "push", "areDependenciesEqual", "nextDependencies", "prevDependencies", "is", "x", "y", "Number", "isNaN", "objectIs", "areHookInputsEqual", "nextDeps", "prevDeps", "length", "i", "isAnimated", "prop", "Array", "isArray", "some", "onFrame", "shallowEqual", "a", "b", "a<PERSON><PERSON><PERSON>", "keys", "b<PERSON><PERSON><PERSON>", "validateAnimatedStyles", "styles"], "sourceRoot": "../../../src", "sources": ["hook/utils.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,eAAe,QAAQ,cAAW;AAG3C;AACA,OAAO,SAASC,iBAAiBA,CAC/BC,QAEwC,EACxC;EACA;EACA,OAAOC,MAAM,CAACC,MAAM,CAACF,QAAQ,CAAC,CAACG,MAAM,CACnC,CAACC,GAAG,EAAEC,OAA2C,KAC/CD,GAAG,GAAGC,OAAO,CAACC,aAAa,CAACC,QAAQ,CAAC,CAAC,EACxC,EACF,CAAC;AACH;;AAEA;AACA,OAAO,SAASC,iBAAiBA,CAC/BC,YAA4B,EAC5BC,QAAqD,EACrD;EAEA,MAAMC,YAAY,GAAGV,MAAM,CAACC,MAAM,CAACQ,QAAQ,CAAC,CAACE,MAAM,CAChDC,OAAO,IAAKA,OAAO,KAAKC,SAC3B,CAA2B;EAC3B,IAAI,CAACL,YAAY,EAAE;IACjBA,YAAY,GAAGE,YAAY,CAACI,GAAG,CAAEF,OAAO,IAAK;MAC3C,OAAO;QACLG,WAAW,EAAEH,OAAO,CAACP,aAAa;QAClCW,OAAO,EAAEJ,OAAO,CAACK;MACnB,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,MAAM;IACLT,YAAY,CAACU,IAAI,CAACpB,iBAAiB,CAACY,YAAY,CAAC,CAAC;EACpD;EAEA,OAAOF,YAAY;AACrB;;AAEA;AACA,OAAO,SAASW,oBAAoBA,CAClCC,gBAAgC,EAChCC,gBAAgC,EAChC;EACA,SAASC,EAAEA,CAACC,CAAS,EAAEC,CAAS,EAAE;IAChC,OACGD,CAAC,KAAKC,CAAC,KAAKD,CAAC,KAAK,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGC,CAAC,CAAC,IACvCC,MAAM,CAACC,KAAK,CAACH,CAAC,CAAC,IAAIE,MAAM,CAACC,KAAK,CAACF,CAAC,CAAE;EAExC;EACA,MAAMG,QAA2D,GAC/D,OAAO3B,MAAM,CAACsB,EAAE,KAAK,UAAU,GAAGtB,MAAM,CAACsB,EAAE,GAAGA,EAAE;EAElD,SAASM,kBAAkBA,CACzBC,QAAwB,EACxBC,QAAwB,EACxB;IACA,IAAI,CAACD,QAAQ,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,MAAM,KAAKF,QAAQ,CAACE,MAAM,EAAE;MACjE,OAAO,KAAK;IACd;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACC,MAAM,EAAE,EAAEC,CAAC,EAAE;MACxC,IAAI,CAACL,QAAQ,CAACE,QAAQ,CAACG,CAAC,CAAC,EAAEF,QAAQ,CAACE,CAAC,CAAC,CAAC,EAAE;QACvC,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;EAEA,OAAOJ,kBAAkB,CAACR,gBAAgB,EAAEC,gBAAgB,CAAC;AAC/D;AAEA,OAAO,SAASY,UAAUA,CAACC,IAAa,EAAE;EACxC,SAAS;;EACT,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI,CAACG,IAAI,CAACJ,UAAU,CAAC;EAC9B,CAAC,MAAM,IAAI,OAAOC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;IACpD,IAAKA,IAAI,CAA6BI,OAAO,KAAKzB,SAAS,EAAE;MAC3D,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAOb,MAAM,CAACC,MAAM,CAACiC,IAAI,CAAC,CAACG,IAAI,CAACJ,UAAU,CAAC;IAC7C;EACF;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA,OAAO,SAASM,YAAYA,CAE1BC,CAAI,EAAEC,CAAI,EAAE;EACZ,SAAS;;EACT,MAAMC,KAAK,GAAG1C,MAAM,CAAC2C,IAAI,CAACH,CAAC,CAAC;EAC5B,MAAMI,KAAK,GAAG5C,MAAM,CAAC2C,IAAI,CAACF,CAAC,CAAC;EAC5B,IAAIC,KAAK,CAACX,MAAM,KAAKa,KAAK,CAACb,MAAM,EAAE;IACjC,OAAO,KAAK;EACd;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,KAAK,CAACX,MAAM,EAAEC,CAAC,EAAE,EAAE;IACrC,IAAIQ,CAAC,CAACE,KAAK,CAACV,CAAC,CAAC,CAAC,KAAKS,CAAC,CAACC,KAAK,CAACV,CAAC,CAAC,CAAC,EAAE;MAC/B,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAEA,OAAO,SAASa,sBAAsBA,CAACC,MAA0B,EAAE;EACjE,SAAS;;EACT,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAM,IAAIjD,eAAe,CACvB,uDAAuD,OAAOiD,MAAM,WACtE,CAAC;EACH,CAAC,MAAM,IAAIX,KAAK,CAACC,OAAO,CAACU,MAAM,CAAC,EAAE;IAChC,MAAM,IAAIjD,eAAe,CACvB,4JACF,CAAC;EACH;AACF", "ignoreList": []}