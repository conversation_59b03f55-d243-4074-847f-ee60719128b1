{"version": 3, "names": ["blue", "green", "hsvToColor", "opacity", "processColor", "red", "rgbaColor", "RGBtoHSV", "makeMutable", "culori", "ReanimatedError", "useSharedValue", "Extrapolation", "interpolate", "Extrapolate", "interpolateColorsHSV", "value", "inputRange", "colors", "options", "h", "useCorrectedHSVInterpolation", "correctedInputRange", "originalH", "correctedH", "i", "length", "d", "push", "CLAMP", "s", "v", "a", "toLinearSpace", "x", "gamma", "map", "Math", "pow", "toGammaSpace", "round", "interpolateColorsRGB", "r", "outputR", "g", "outputG", "b", "outputB", "interpolateColorsLAB", "_options", "l", "alpha", "_r", "_g", "_b", "_alpha", "oklab", "convert", "toRgb", "_splitColorsIntoChannels", "convFromRgb", "ch1", "ch2", "ch3", "color", "processedColor", "convertedColor", "getInterpolateRGB", "getInterpolateHSV", "hsvColor", "getInterpolateLAB", "labColor", "fromRgb", "interpolateColor", "outputRange", "colorSpace", "ColorSpace", "useInterpolateConfig", "RGB", "cache"], "sourceRoot": "../../src", "sources": ["interpolateColor.ts"], "mappings": "AAAA,YAAY;;AACZ,SACEA,IAAI,EACJC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,YAAY,EACZC,GAAG,EACHC,SAAS,EACTC,QAAQ,QACH,aAAU;AAEjB,SAASC,WAAW,QAAQ,WAAQ;AACpC,OAAOC,MAAM,MAAM,mBAAU;AAC7B,SAASC,eAAe,QAAQ,aAAU;AAC1C,SAASC,cAAc,QAAQ,0BAAuB;AACtD,SAASC,aAAa,EAAEC,WAAW,QAAQ,oBAAiB;;AAE5D;AACA,OAAO,MAAMC,WAAW,GAAGF,aAAa;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA,MAAMG,oBAAoB,GAAGA,CAC3BC,KAAa,EACbC,UAA6B,EAC7BC,MAAsB,EACtBC,OAA6B,KAC1B;EACH,SAAS;;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,MAAM;IAAEC,4BAA4B,GAAG;EAAK,CAAC,GAAGF,OAAO;EACvD,IAAIE,4BAA4B,EAAE;IAChC;IACA;IACA;IACA;IACA,MAAMC,mBAAmB,GAAG,CAACL,UAAU,CAAC,CAAC,CAAC,CAAC;IAC3C,MAAMM,SAAS,GAAGL,MAAM,CAACE,CAAC;IAC1B,MAAMI,UAAU,GAAG,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC;IAEjC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAE,EAAED,CAAC,EAAE;MACzC,MAAME,CAAC,GAAGJ,SAAS,CAACE,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC;MACzC,IAAIF,SAAS,CAACE,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC,IAAIE,CAAC,GAAG,GAAG,EAAE;QAC9CL,mBAAmB,CAACM,IAAI,CAACX,UAAU,CAACQ,CAAC,CAAC,CAAC;QACvCH,mBAAmB,CAACM,IAAI,CAACX,UAAU,CAACQ,CAAC,CAAC,GAAG,OAAO,CAAC;QACjDD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,GAAG,CAAC,CAAC;QACjCD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAC;MAC/B,CAAC,MAAM,IAAIF,SAAS,CAACE,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC,IAAIE,CAAC,GAAG,CAAC,GAAG,EAAE;QACtDL,mBAAmB,CAACM,IAAI,CAACX,UAAU,CAACQ,CAAC,CAAC,CAAC;QACvCH,mBAAmB,CAACM,IAAI,CAACX,UAAU,CAACQ,CAAC,CAAC,GAAG,OAAO,CAAC;QACjDD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,GAAG,CAAC,CAAC;QACjCD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAC;MAC/B,CAAC,MAAM;QACLH,mBAAmB,CAACM,IAAI,CAACX,UAAU,CAACQ,CAAC,CAAC,CAAC;QACvCD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAC;MAC/B;IACF;IACAL,CAAC,GACC,CAACP,WAAW,CACVG,KAAK,EACLM,mBAAmB,EACnBE,UAAU,EACVZ,aAAa,CAACiB,KAChB,CAAC,GACC,CAAC,IACH,CAAC;EACL,CAAC,MAAM;IACLT,CAAC,GAAGP,WAAW,CAACG,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACE,CAAC,EAAER,aAAa,CAACiB,KAAK,CAAC;EACnE;EACA,MAAMC,CAAC,GAAGjB,WAAW,CAACG,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACY,CAAC,EAAElB,aAAa,CAACiB,KAAK,CAAC;EACvE,MAAME,CAAC,GAAGlB,WAAW,CAACG,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACa,CAAC,EAAEnB,aAAa,CAACiB,KAAK,CAAC;EACvE,MAAMG,CAAC,GAAGnB,WAAW,CAACG,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACc,CAAC,EAAEpB,aAAa,CAACiB,KAAK,CAAC;EACvE,OAAO3B,UAAU,CAACkB,CAAC,EAAEU,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;AAC/B,CAAC;AAED,MAAMC,aAAa,GAAGA,CAACC,CAAW,EAAEC,KAAa,KAAe;EAC9D,SAAS;;EACT,OAAOD,CAAC,CAACE,GAAG,CAAEL,CAAC,IAAKM,IAAI,CAACC,GAAG,CAACP,CAAC,GAAG,GAAG,EAAEI,KAAK,CAAC,CAAC;AAC/C,CAAC;AAED,MAAMI,YAAY,GAAGA,CAACL,CAAS,EAAEC,KAAa,KAAa;EACzD,SAAS;;EACT,OAAOE,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,GAAG,CAACJ,CAAC,EAAE,CAAC,GAAGC,KAAK,CAAC,GAAG,GAAG,CAAC;AACjD,CAAC;AAED,MAAMM,oBAAoB,GAAGA,CAC3BzB,KAAa,EACbC,UAA6B,EAC7BC,MAAsB,EACtBC,OAA6B,KAC1B;EACH,SAAS;;EACT,MAAM;IAAEgB,KAAK,GAAG;EAAI,CAAC,GAAGhB,OAAO;EAC/B,IAAI;IAAEuB,CAAC,EAAEC,OAAO;IAAEC,CAAC,EAAEC,OAAO;IAAEC,CAAC,EAAEC;EAAQ,CAAC,GAAG7B,MAAM;EACnD,IAAIiB,KAAK,KAAK,CAAC,EAAE;IACfQ,OAAO,GAAGV,aAAa,CAACU,OAAO,EAAER,KAAK,CAAC;IACvCU,OAAO,GAAGZ,aAAa,CAACY,OAAO,EAAEV,KAAK,CAAC;IACvCY,OAAO,GAAGd,aAAa,CAACc,OAAO,EAAEZ,KAAK,CAAC;EACzC;EACA,MAAMO,CAAC,GAAG7B,WAAW,CAACG,KAAK,EAAEC,UAAU,EAAE0B,OAAO,EAAE/B,aAAa,CAACiB,KAAK,CAAC;EACtE,MAAMe,CAAC,GAAG/B,WAAW,CAACG,KAAK,EAAEC,UAAU,EAAE4B,OAAO,EAAEjC,aAAa,CAACiB,KAAK,CAAC;EACtE,MAAMiB,CAAC,GAAGjC,WAAW,CAACG,KAAK,EAAEC,UAAU,EAAE8B,OAAO,EAAEnC,aAAa,CAACiB,KAAK,CAAC;EACtE,MAAMG,CAAC,GAAGnB,WAAW,CAACG,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACc,CAAC,EAAEpB,aAAa,CAACiB,KAAK,CAAC;EACvE,IAAIM,KAAK,KAAK,CAAC,EAAE;IACf,OAAO7B,SAAS,CAACoC,CAAC,EAAEE,CAAC,EAAEE,CAAC,EAAEd,CAAC,CAAC;EAC9B;EACA,OAAO1B,SAAS,CACdiC,YAAY,CAACG,CAAC,EAAEP,KAAK,CAAC,EACtBI,YAAY,CAACK,CAAC,EAAET,KAAK,CAAC,EACtBI,YAAY,CAACO,CAAC,EAAEX,KAAK,CAAC,EACtBH,CACF,CAAC;AACH,CAAC;AAED,MAAMgB,oBAAoB,GAAGA,CAC3BhC,KAAa,EACbC,UAA6B,EAC7BC,MAAsB,EACtB+B,QAA8B,KAC3B;EACH,SAAS;;EACT,MAAMC,CAAC,GAAGrC,WAAW,CAACG,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACgC,CAAC,EAAEtC,aAAa,CAACiB,KAAK,CAAC;EACvE,MAAMG,CAAC,GAAGnB,WAAW,CAACG,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACc,CAAC,EAAEpB,aAAa,CAACiB,KAAK,CAAC;EACvE,MAAMiB,CAAC,GAAGjC,WAAW,CAACG,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAAC4B,CAAC,EAAElC,aAAa,CAACiB,KAAK,CAAC;EACvE,MAAMsB,KAAK,GAAGtC,WAAW,CACvBG,KAAK,EACLC,UAAU,EACVC,MAAM,CAACiC,KAAK,EACZvC,aAAa,CAACiB,KAChB,CAAC;EACD,MAAM;IACJa,CAAC,EAAEU,EAAE;IACLR,CAAC,EAAES,EAAE;IACLP,CAAC,EAAEQ,EAAE;IACLH,KAAK,EAAEI;EACT,CAAC,GAAG9C,MAAM,CAAC+C,KAAK,CAACC,OAAO,CAACC,KAAK,CAAC;IAAER,CAAC;IAAElB,CAAC;IAAEc,CAAC;IAAEK;EAAM,CAAC,CAAC;EAClD,OAAO7C,SAAS,CAAC8C,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,MAAM,CAAC;AACtC,CAAC;AAED,MAAMI,wBAAwB,GAAGA,CAC/BzC,MAAoC,EACpC0C,WAIC,KAME;EACH,SAAS;;EACT,MAAMC,GAAa,GAAG,EAAE;EACxB,MAAMC,GAAa,GAAG,EAAE;EACxB,MAAMC,GAAa,GAAG,EAAE;EACxB,MAAMZ,KAAe,GAAG,EAAE;EAE1B,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,MAAM,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,MAAMuC,KAAK,GAAG9C,MAAM,CAACO,CAAC,CAAC;IACvB,MAAMwC,cAAc,GAAG7D,YAAY,CAAC4D,KAAK,CAAC;IAC1C,IAAI,OAAOC,cAAc,KAAK,QAAQ,EAAE;MACtC,MAAMC,cAAc,GAAGN,WAAW,CAAC;QACjClB,CAAC,EAAErC,GAAG,CAAC4D,cAAc,CAAC;QACtBrB,CAAC,EAAE3C,KAAK,CAACgE,cAAc,CAAC;QACxBnB,CAAC,EAAE9C,IAAI,CAACiE,cAAc;MACxB,CAAC,CAAC;MAEFJ,GAAG,CAACjC,IAAI,CAACsC,cAAc,CAACL,GAAG,CAAC;MAC5BC,GAAG,CAAClC,IAAI,CAACsC,cAAc,CAACJ,GAAG,CAAC;MAC5BC,GAAG,CAACnC,IAAI,CAACsC,cAAc,CAACH,GAAG,CAAC;MAC5BZ,KAAK,CAACvB,IAAI,CAACzB,OAAO,CAAC8D,cAAc,CAAC,CAAC;IACrC;EACF;EAEA,OAAO;IACLJ,GAAG;IACHC,GAAG;IACHC,GAAG;IACHZ;EACF,CAAC;AACH,CAAC;AASD,MAAMgB,iBAAiB,GACrBjD,MAAoC,IACjB;EACnB,SAAS;;EACT,MAAM;IAAE2C,GAAG;IAAEC,GAAG;IAAEC,GAAG;IAAEZ;EAAM,CAAC,GAAGQ,wBAAwB,CACvDzC,MAAM,EACL8C,KAAK,KAAM;IACVH,GAAG,EAAEG,KAAK,CAACtB,CAAC;IACZoB,GAAG,EAAEE,KAAK,CAACpB,CAAC;IACZmB,GAAG,EAAEC,KAAK,CAAClB;EACb,CAAC,CACH,CAAC;EAED,OAAO;IACLJ,CAAC,EAAEmB,GAAG;IACNjB,CAAC,EAAEkB,GAAG;IACNhB,CAAC,EAAEiB,GAAG;IACN/B,CAAC,EAAEmB;EACL,CAAC;AACH,CAAC;AASD,MAAMiB,iBAAiB,GACrBlD,MAAoC,IACjB;EACnB,SAAS;;EACT,MAAM;IAAE2C,GAAG;IAAEC,GAAG;IAAEC,GAAG;IAAEZ;EAAM,CAAC,GAAGQ,wBAAwB,CAACzC,MAAM,EAAG8C,KAAK,IAAK;IAC3E,MAAMK,QAAQ,GAAG9D,QAAQ,CAACyD,KAAK,CAACtB,CAAC,EAAEsB,KAAK,CAACpB,CAAC,EAAEoB,KAAK,CAAClB,CAAC,CAAC;IACpD,OAAO;MACLe,GAAG,EAAEQ,QAAQ,CAACjD,CAAC;MACf0C,GAAG,EAAEO,QAAQ,CAACvC,CAAC;MACfiC,GAAG,EAAEM,QAAQ,CAACtC;IAChB,CAAC;EACH,CAAC,CAAC;EAEF,OAAO;IACLX,CAAC,EAAEyC,GAAG;IACN/B,CAAC,EAAEgC,GAAG;IACN/B,CAAC,EAAEgC,GAAG;IACN/B,CAAC,EAAEmB;EACL,CAAC;AACH,CAAC;AASD,MAAMmB,iBAAiB,GACrBpD,MAAoC,IACjB;EACnB,SAAS;;EAET,MAAM;IAAE2C,GAAG;IAAEC,GAAG;IAAEC,GAAG;IAAEZ;EAAM,CAAC,GAAGQ,wBAAwB,CAACzC,MAAM,EAAG8C,KAAK,IAAK;IAC3E,MAAMO,QAAQ,GAAG9D,MAAM,CAAC+C,KAAK,CAACC,OAAO,CAACe,OAAO,CAACR,KAAK,CAAC;IACpD,OAAO;MACLH,GAAG,EAAEU,QAAQ,CAACrB,CAAC;MACfY,GAAG,EAAES,QAAQ,CAACvC,CAAC;MACf+B,GAAG,EAAEQ,QAAQ,CAACzB;IAChB,CAAC;EACH,CAAC,CAAC;EAEF,OAAO;IACLI,CAAC,EAAEW,GAAG;IACN7B,CAAC,EAAE8B,GAAG;IACNhB,CAAC,EAAEiB,GAAG;IACNZ;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAiBA,OAAO,SAASsB,gBAAgBA,CAC9BzD,KAAa,EACbC,UAA6B,EAC7ByD,WAAyC,EACzCC,UAAiC,GAAG,KAAK,EACzCxD,OAA6B,GAAG,CAAC,CAAC,EACjB;EACjB,SAAS;;EACT,IAAIwD,UAAU,KAAK,KAAK,EAAE;IACxB,OAAO5D,oBAAoB,CACzBC,KAAK,EACLC,UAAU,EACVmD,iBAAiB,CAACM,WAAW,CAAC,EAC9BvD,OACF,CAAC;EACH,CAAC,MAAM,IAAIwD,UAAU,KAAK,KAAK,EAAE;IAC/B,OAAOlC,oBAAoB,CACzBzB,KAAK,EACLC,UAAU,EACVkD,iBAAiB,CAACO,WAAW,CAAC,EAC9BvD,OACF,CAAC;EACH,CAAC,MAAM,IAAIwD,UAAU,KAAK,KAAK,EAAE;IAC/B,OAAO3B,oBAAoB,CACzBhC,KAAK,EACLC,UAAU,EACVqD,iBAAiB,CAACI,WAAW,CAAC,EAC9BvD,OACF,CAAC;EACH;EAEA,MAAM,IAAIT,eAAe,CACvB,iCACEiE,UAAU,gDAEd,CAAC;AACH;AAEA,WAAYC,UAAU,0BAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA;AActB,OAAO,SAASC,oBAAoBA,CAClC5D,UAA6B,EAC7ByD,WAAyC,EACzCC,UAAU,GAAGC,UAAU,CAACE,GAAG,EAC3B3D,OAA6B,GAAG,CAAC,CAAC,EACF;EAChC,OAAOR,cAAc,CAAoB;IACvCM,UAAU;IACVyD,WAAW;IACXC,UAAU;IACVI,KAAK,EAAEvE,WAAW,CAAyC,IAAI,CAAC;IAChEW;EACF,CAAC,CAAC;AACJ", "ignoreList": []}