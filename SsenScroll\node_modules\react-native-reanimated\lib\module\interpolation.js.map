{"version": 3, "names": ["ReanimatedError", "Extrapolation", "getVal", "type", "coef", "val", "leftEdgeOutput", "rightEdgeOutput", "x", "IDENTITY", "CLAMP", "EXTEND", "isExtrapolate", "value", "validateType", "extrapolationConfig", "extrapolateLeft", "extrapolateRight", "Object", "assign", "internalInterpolate", "narrowedInput", "leftEdgeInput", "rightEdgeInput", "progress", "interpolate", "inputRange", "outputRange", "length", "i", "clamp", "min", "max", "Math"], "sourceRoot": "../../src", "sources": ["interpolation.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,eAAe,QAAQ,aAAU;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAYC,aAAa,0BAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAA,OAAbA,aAAa;AAAA;;AAMzB;;AAUA;;AAWA;;AAOA,SAASC,MAAMA,CACbC,IAAmB,EACnBC,IAAY,EACZC,GAAW,EACXC,cAAsB,EACtBC,eAAuB,EACvBC,CAAS,EACD;EACR,SAAS;;EAET,QAAQL,IAAI;IACV,KAAKF,aAAa,CAACQ,QAAQ;MACzB,OAAOD,CAAC;IACV,KAAKP,aAAa,CAACS,KAAK;MACtB,IAAIN,IAAI,GAAGC,GAAG,GAAGD,IAAI,GAAGE,cAAc,EAAE;QACtC,OAAOA,cAAc;MACvB;MACA,OAAOC,eAAe;IACxB,KAAKN,aAAa,CAACU,MAAM;IACzB;MACE,OAAON,GAAG;EACd;AACF;AAEA,SAASO,aAAaA,CAACC,KAAa,EAA0B;EAC5D,SAAS;;EAET,OACE;IACAA,KAAK,KAAKZ,aAAa,CAACU,MAAM,IAC9BE,KAAK,KAAKZ,aAAa,CAACS,KAAK,IAC7BG,KAAK,KAAKZ,aAAa,CAACQ;IACxB;EAAA;AAEJ;;AAEA;AACA;AACA,SAASK,YAAYA,CAACX,IAAuB,EAA+B;EAC1E,SAAS;;EACT;EACA,MAAMY,mBAAgD,GAAG;IACvDC,eAAe,EAAEf,aAAa,CAACU,MAAM;IACrCM,gBAAgB,EAAEhB,aAAa,CAACU;EAClC,CAAC;EAED,IAAI,CAACR,IAAI,EAAE;IACT,OAAOY,mBAAmB;EAC5B;EAEA,IAAI,OAAOZ,IAAI,KAAK,QAAQ,EAAE;IAC5B,IAAI,CAACS,aAAa,CAACT,IAAI,CAAC,EAAE;MACxB,MAAM,IAAIH,eAAe,CACvB;AACR,iEACM,CAAC;IACH;IACAe,mBAAmB,CAACC,eAAe,GAAGb,IAAI;IAC1CY,mBAAmB,CAACE,gBAAgB,GAAGd,IAAI;IAC3C,OAAOY,mBAAmB;EAC5B;;EAEA;EACA,IACGZ,IAAI,CAACa,eAAe,IAAI,CAACJ,aAAa,CAACT,IAAI,CAACa,eAAe,CAAC,IAC5Db,IAAI,CAACc,gBAAgB,IAAI,CAACL,aAAa,CAACT,IAAI,CAACc,gBAAgB,CAAE,EAChE;IACA,MAAM,IAAIjB,eAAe,CACvB;AACN;AACA;AACA;AACA,UACI,CAAC;EACH;EAEAkB,MAAM,CAACC,MAAM,CAACJ,mBAAmB,EAAEZ,IAAI,CAAC;EACxC,OAAOY,mBAAmB;AAC5B;AAEA,SAASK,mBAAmBA,CAC1BZ,CAAS,EACTa,aAAyC,EACzCN,mBAAgD,EAChD;EACA,SAAS;;EACT,MAAM;IAAEO,aAAa;IAAEC,cAAc;IAAEjB,cAAc;IAAEC;EAAgB,CAAC,GACtEc,aAAa;EACf,IAAIE,cAAc,GAAGD,aAAa,KAAK,CAAC,EAAE;IACxC,OAAOhB,cAAc;EACvB;EACA,MAAMkB,QAAQ,GAAG,CAAChB,CAAC,GAAGc,aAAa,KAAKC,cAAc,GAAGD,aAAa,CAAC;EACvE,MAAMjB,GAAG,GAAGC,cAAc,GAAGkB,QAAQ,IAAIjB,eAAe,GAAGD,cAAc,CAAC;EAC1E,MAAMF,IAAI,GAAGG,eAAe,IAAID,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;EAEvD,IAAIF,IAAI,GAAGC,GAAG,GAAGD,IAAI,GAAGE,cAAc,EAAE;IACtC,OAAOJ,MAAM,CACXa,mBAAmB,CAACC,eAAe,EACnCZ,IAAI,EACJC,GAAG,EACHC,cAAc,EACdC,eAAe,EACfC,CACF,CAAC;EACH,CAAC,MAAM,IAAIJ,IAAI,GAAGC,GAAG,GAAGD,IAAI,GAAGG,eAAe,EAAE;IAC9C,OAAOL,MAAM,CACXa,mBAAmB,CAACE,gBAAgB,EACpCb,IAAI,EACJC,GAAG,EACHC,cAAc,EACdC,eAAe,EACfC,CACF,CAAC;EACH;EAEA,OAAOH,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASoB,WAAWA,CACzBjB,CAAS,EACTkB,UAA6B,EAC7BC,WAA8B,EAC9BxB,IAAwB,EAChB;EACR,SAAS;;EACT,IAAIuB,UAAU,CAACE,MAAM,GAAG,CAAC,IAAID,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;IACnD,MAAM,IAAI5B,eAAe,CACvB,2EACF,CAAC;EACH;EAEA,MAAMe,mBAAmB,GAAGD,YAAY,CAACX,IAAI,CAAC;EAC9C,MAAMyB,MAAM,GAAGF,UAAU,CAACE,MAAM;EAChC,MAAMP,aAAyC,GAAG;IAChDC,aAAa,EAAEI,UAAU,CAAC,CAAC,CAAC;IAC5BH,cAAc,EAAEG,UAAU,CAAC,CAAC,CAAC;IAC7BpB,cAAc,EAAEqB,WAAW,CAAC,CAAC,CAAC;IAC9BpB,eAAe,EAAEoB,WAAW,CAAC,CAAC;EAChC,CAAC;EACD,IAAIC,MAAM,GAAG,CAAC,EAAE;IACd,IAAIpB,CAAC,GAAGkB,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC,EAAE;MAC9BP,aAAa,CAACC,aAAa,GAAGI,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC;MACpDP,aAAa,CAACE,cAAc,GAAGG,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC;MACrDP,aAAa,CAACf,cAAc,GAAGqB,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC;MACtDP,aAAa,CAACd,eAAe,GAAGoB,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC;IACzD,CAAC,MAAM;MACL,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,EAAE,EAAEC,CAAC,EAAE;QAC/B,IAAIrB,CAAC,IAAIkB,UAAU,CAACG,CAAC,CAAC,EAAE;UACtBR,aAAa,CAACC,aAAa,GAAGI,UAAU,CAACG,CAAC,GAAG,CAAC,CAAC;UAC/CR,aAAa,CAACE,cAAc,GAAGG,UAAU,CAACG,CAAC,CAAC;UAC5CR,aAAa,CAACf,cAAc,GAAGqB,WAAW,CAACE,CAAC,GAAG,CAAC,CAAC;UACjDR,aAAa,CAACd,eAAe,GAAGoB,WAAW,CAACE,CAAC,CAAC;UAC9C;QACF;MACF;IACF;EACF;EAEA,OAAOT,mBAAmB,CAACZ,CAAC,EAAEa,aAAa,EAAEN,mBAAmB,CAAC;AACnE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASe,KAAKA,CAACjB,KAAa,EAAEkB,GAAW,EAAEC,GAAW,EAAE;EAC7D,SAAS;;EACT,OAAOC,IAAI,CAACF,GAAG,CAACE,IAAI,CAACD,GAAG,CAACnB,KAAK,EAAEkB,GAAG,CAAC,EAAEC,GAAG,CAAC;AAC5C", "ignoreList": []}