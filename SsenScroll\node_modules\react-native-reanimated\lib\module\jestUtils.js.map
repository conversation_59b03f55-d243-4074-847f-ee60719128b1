{"version": 3, "names": ["ReanimatedError", "isJest", "defaultFramerateConfig", "fps", "isEmpty", "obj", "Object", "keys", "length", "getStylesFromObject", "undefined", "fromEntries", "entries", "map", "property", "value", "_isReanimatedSharedValue", "getCurrentProps", "component", "propsObject", "props", "jestAnimatedProps", "getCurrentStyle", "styleObject", "style", "currentStyle", "Array", "isArray", "for<PERSON>ach", "jestInlineStyles", "jestInlineStyle", "jestAnimatedStyleValue", "jestAnimatedStyle", "inlineStyles", "checkEqual", "current", "expected", "i", "findStyleDiff", "shouldMatchAllProps", "diffs", "isEqual", "push", "expect", "compareAndFormatDifferences", "currentV<PERSON>ues", "expectedV<PERSON>ues", "message", "pass", "currentValuesStr", "JSON", "stringify", "expectedValuesStr", "differences", "diff", "join", "compareProps", "expectedProps", "currentProps", "compareStyle", "expectedStyle", "config", "frameTime", "Math", "round", "beforeTest", "jest", "useFakeTimers", "afterTest", "runOnlyPendingTimers", "useRealTimers", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animationTest", "console", "warn", "advanceAnimationByTime", "time", "advanceTimersByTime", "advanceAnimationByFrame", "count", "requireFunction", "require", "setUpTests", "userFramerateConfig", "global", "expectModule", "jestGlobals", "extend", "default", "framerateConfig", "toHaveAnimatedProps", "toHaveAnimatedStyle", "getAnimatedStyle"], "sourceRoot": "../../src", "sources": ["jestUtils.ts"], "mappings": "AAAA;AACA,YAAY;;AAUZ,SAASA,eAAe,QAAQ,aAAU;AAE1C,SAASC,MAAM,QAAQ,sBAAmB;AAgB1C,MAAMC,sBAAsB,GAAG;EAC7BC,GAAG,EAAE;AACP,CAAC;AAED,MAAMC,OAAO,GAAIC,GAAuB,IACtC,CAACA,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAACG,MAAM,KAAK,CAAC;AACvC,MAAMC,mBAAmB,GAAIJ,GAAW,IAAK;EAC3C,OAAOA,GAAG,KAAKK,SAAS,GACpB,CAAC,CAAC,GACFJ,MAAM,CAACK,WAAW,CAChBL,MAAM,CAACM,OAAO,CAACP,GAAG,CAAC,CAACQ,GAAG,CAAC,CAAC,CAACC,QAAQ,EAAEC,KAAK,CAAC,KAAK,CAC7CD,QAAQ,EACRC,KAAK,CAACC,wBAAwB,GAAGD,KAAK,CAACA,KAAK,GAAGA,KAAK,CACrD,CACH,CAAC;AACP,CAAC;AASD,MAAME,eAAe,GACnBC,SAAwB,IAC2B;EACnD,MAAMC,WAAW,GAAGD,SAAS,CAACE,KAAK,CAACC,iBAAiB,EAAEN,KAAK;EAE5D,OAAOI,WAAW,GAAG;IAAE,GAAGA;EAAY,CAAC,GAAG,CAAC,CAAC;AAC9C,CAAC;AAED,MAAMG,eAAe,GAAIJ,SAAwB,IAAmB;EAClE,MAAMK,WAAW,GAAGL,SAAS,CAACE,KAAK,CAACI,KAAK;EAEzC,IAAIC,YAAY,GAAG,CAAC,CAAC;EAErB,IAAIC,KAAK,CAACC,OAAO,CAACJ,WAAW,CAAC,EAAE;IAC9B;IACA;IACAA,WAAW,CAACK,OAAO,CAAEJ,KAAK,IAAK;MAC7BC,YAAY,GAAG;QACb,GAAGA,YAAY;QACf,GAAGD;MACL,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,MAAMK,gBAAgB,GAAGX,SAAS,CAACE,KAAK,CAACU,eAAkC;EAC3E,MAAMC,sBAAsB,GAAGb,SAAS,CAACE,KAAK,CAACY,iBAAiB,EAAEjB,KAAK;EAEvE,IAAIW,KAAK,CAACC,OAAO,CAACE,gBAAgB,CAAC,EAAE;IACnC,KAAK,MAAMxB,GAAG,IAAIwB,gBAAgB,EAAE;MAClC,IAAI,oBAAoB,IAAIxB,GAAG,EAAE;QAC/B;MACF;MAEA,MAAM4B,YAAY,GAAGxB,mBAAmB,CAACJ,GAAG,CAAC;MAE7CoB,YAAY,GAAG;QACb,GAAGA,YAAY;QACf,GAAGQ;MACL,CAAC;IACH;IAEAR,YAAY,GAAG;MACb,GAAGA,YAAY;MACf,GAAGM;IACL,CAAC;IAED,OAAON,YAAY;EACrB;EAEA,MAAMQ,YAAY,GAAGxB,mBAAmB,CAACoB,gBAAgB,CAAC;EAE1DJ,YAAY,GAAGrB,OAAO,CAAC2B,sBAA4C,CAAC,GAChE;IAAE,GAAGE;EAAa,CAAC,GACnB;IAAE,GAAGF;EAAuB,CAAC;EAEjC,OAAON,YAAY;AACrB,CAAC;AAED,MAAMS,UAAU,GAAGA,CAAQC,OAAc,EAAEC,QAAe,KAAK;EAC7D,IAAIV,KAAK,CAACC,OAAO,CAACS,QAAQ,CAAC,EAAE;IAC3B,IAAI,CAACV,KAAK,CAACC,OAAO,CAACQ,OAAO,CAAC,IAAIC,QAAQ,CAAC5B,MAAM,KAAK2B,OAAO,CAAC3B,MAAM,EAAE;MACjE,OAAO,KAAK;IACd;IACA,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAAC3B,MAAM,EAAE6B,CAAC,EAAE,EAAE;MACvC,IAAI,CAACH,UAAU,CAACC,OAAO,CAACE,CAAC,CAAC,EAAED,QAAQ,CAACC,CAAC,CAAC,CAAC,EAAE;QACxC,OAAO,KAAK;MACd;IACF;EACF,CAAC,MAAM,IAAI,OAAOF,OAAO,KAAK,QAAQ,IAAIA,OAAO,EAAE;IACjD,IAAI,OAAOC,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,EAAE;MAC7C,OAAO,KAAK;IACd;IACA,KAAK,MAAMtB,QAAQ,IAAIsB,QAAQ,EAAE;MAC/B,IAAI,CAACF,UAAU,CAACC,OAAO,CAACrB,QAAQ,CAAC,EAAEsB,QAAQ,CAACtB,QAAQ,CAAC,CAAC,EAAE;QACtD,OAAO,KAAK;MACd;IACF;EACF,CAAC,MAAM;IACL,OAAOqB,OAAO,KAAKC,QAAQ;EAC7B;EACA,OAAO,IAAI;AACb,CAAC;AAED,MAAME,aAAa,GAAGA,CACpBH,OAAsE,EACtEC,QAAuE,EACvEG,mBAA6B,KAC1B;EACH,MAAMC,KAAK,GAAG,EAAE;EAChB,IAAIC,OAAO,GAAG,IAAI;EAClB,IAAI3B,QAA4B;EAChC,KAAKA,QAAQ,IAAIsB,QAAQ,EAAE;IACzB,IAAI,CAACF,UAAU,CAACC,OAAO,CAACrB,QAAQ,CAAC,EAAEsB,QAAQ,CAACtB,QAAQ,CAAC,CAAC,EAAE;MACtD2B,OAAO,GAAG,KAAK;MACfD,KAAK,CAACE,IAAI,CAAC;QACT5B,QAAQ;QACRqB,OAAO,EAAEA,OAAO,CAACrB,QAAQ,CAAC;QAC1B6B,MAAM,EAAEP,QAAQ,CAACtB,QAAQ;MAC3B,CAAC,CAAC;IACJ;EACF;EAEA,IACEyB,mBAAmB,IACnBjC,MAAM,CAACC,IAAI,CAAC4B,OAAO,CAAC,CAAC3B,MAAM,KAAKF,MAAM,CAACC,IAAI,CAAC6B,QAAQ,CAAC,CAAC5B,MAAM,EAC5D;IACAiC,OAAO,GAAG,KAAK;IACf;IACA,IAAI3B,QAA4B;IAChC,KAAKA,QAAQ,IAAIqB,OAAO,EAAE;MACxB,IAAIC,QAAQ,CAACtB,QAAQ,CAAC,KAAKJ,SAAS,EAAE;QACpC8B,KAAK,CAACE,IAAI,CAAC;UACT5B,QAAQ;UACRqB,OAAO,EAAEA,OAAO,CAACrB,QAAQ,CAAC;UAC1B6B,MAAM,EAAEP,QAAQ,CAACtB,QAAQ;QAC3B,CAAC,CAAC;MACJ;IACF;EACF;EAEA,OAAO;IAAE2B,OAAO;IAAED;EAAM,CAAC;AAC3B,CAAC;AAED,MAAMI,2BAA2B,GAAGA,CAClCC,aAA4E,EAC5EC,cAA6E,EAC7EP,mBAA4B,GAAG,KAAK,KACS;EAC7C,MAAM;IAAEE,OAAO;IAAED;EAAM,CAAC,GAAGF,aAAa,CACtCO,aAAa,EACbC,cAAc,EACdP,mBACF,CAAC;EAED,IAAIE,OAAO,EAAE;IACX,OAAO;MAAEM,OAAO,EAAEA,CAAA,KAAM,IAAI;MAAEC,IAAI,EAAE;IAAK,CAAC;EAC5C;EAEA,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,SAAS,CAACN,aAAa,CAAC;EACtD,MAAMO,iBAAiB,GAAGF,IAAI,CAACC,SAAS,CAACL,cAAc,CAAC;EACxD,MAAMO,WAAW,GAAGb,KAAK,CACtB3B,GAAG,CACDyC,IAAI,IACH,MAAMA,IAAI,CAACxC,QAAQ,eAAeoC,IAAI,CAACC,SAAS,CAACG,IAAI,CAACX,MAAM,CAAC,YAAYO,IAAI,CAACC,SAAS,CAACG,IAAI,CAACnB,OAAO,CAAC,EACzG,CAAC,CACAoB,IAAI,CAAC,IAAI,CAAC;EAEb,OAAO;IACLR,OAAO,EAAEA,CAAA,KACP,aAAaK,iBAAiB,eAAeH,gBAAgB,qBAAqBI,WAAW,EAAE;IACjGL,IAAI,EAAE;EACR,CAAC;AACH,CAAC;AAED,MAAMQ,YAAY,GAAGA,CACnBtC,SAAwB,EACxBuC,aAA6D,KAC1D;EACH,IACEvC,SAAS,CAACE,KAAK,CAACC,iBAAiB,IACjCf,MAAM,CAACC,IAAI,CAACW,SAAS,CAACE,KAAK,CAACC,iBAAiB,CAACN,KAAK,CAAC,CAACP,MAAM,KAAK,CAAC,EACjE;IACA,OAAO;MAAEuC,OAAO,EAAEA,CAAA,KAAM,+BAA+B;MAAEC,IAAI,EAAE;IAAM,CAAC;EACxE;EAEA,MAAMU,YAAY,GAAGzC,eAAe,CAACC,SAAS,CAAC;EAE/C,OAAO0B,2BAA2B,CAACc,YAAY,EAAED,aAAa,CAAC;AACjE,CAAC;AAED,MAAME,YAAY,GAAGA,CACnBzC,SAAwB,EACxB0C,aAA2B,EAC3BC,MAAiC,KAC9B;EACH,IAAI,CAAC3C,SAAS,CAACE,KAAK,CAACI,KAAK,EAAE;IAC1B,OAAO;MAAEuB,OAAO,EAAEA,CAAA,KAAM,iCAAiC;MAAEC,IAAI,EAAE;IAAM,CAAC;EAC1E;EACA,MAAM;IAAET;EAAoB,CAAC,GAAGsB,MAAM;EACtC,MAAMpC,YAAY,GAAGH,eAAe,CAACJ,SAAS,CAAC;EAE/C,OAAO0B,2BAA2B,CAChCnB,YAAY,EACZmC,aAAa,EACbrB,mBACF,CAAC;AACH,CAAC;AAED,IAAIuB,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,GAAG9D,sBAAsB,CAACC,GAAG,CAAC;AAE7D,MAAM8D,UAAU,GAAGA,CAAA,KAAM;EACvBC,IAAI,CAACC,aAAa,CAAC,CAAC;AACtB,CAAC;AAED,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtBF,IAAI,CAACG,oBAAoB,CAAC,CAAC;EAC3BH,IAAI,CAACI,aAAa,CAAC,CAAC;AACtB,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAIC,aAAyB,IAAK;EAChEC,OAAO,CAACC,IAAI,CACV,sKACF,CAAC;EACDT,UAAU,CAAC,CAAC;EACZO,aAAa,CAAC,CAAC;EACfJ,SAAS,CAAC,CAAC;AACb,CAAC;AAED,OAAO,MAAMO,sBAAsB,GAAGA,CAACC,IAAI,GAAGd,SAAS,KAAK;EAC1DW,OAAO,CAACC,IAAI,CACV,kEACF,CAAC;EACDR,IAAI,CAACW,mBAAmB,CAACD,IAAI,CAAC;EAC9BV,IAAI,CAACG,oBAAoB,CAAC,CAAC;AAC7B,CAAC;AAED,OAAO,MAAMS,uBAAuB,GAAIC,KAAa,IAAK;EACxDN,OAAO,CAACC,IAAI,CACV,kEACF,CAAC;EACDR,IAAI,CAACW,mBAAmB,CAACE,KAAK,GAAGjB,SAAS,CAAC;EAC3CI,IAAI,CAACG,oBAAoB,CAAC,CAAC;AAC7B,CAAC;AAED,MAAMW,eAAe,GAAG/E,MAAM,CAAC,CAAC,GAC5BgF,OAAO,GACP,MAAM;EACJ,MAAM,IAAIjF,eAAe,CACvB,qDACF,CAAC;AACH,CAAC;AAML,OAAO,MAAMkF,UAAU,GAAGA,CAACC,mBAAmB,GAAG,CAAC,CAAC,KAAK;EACtD,IAAIxC,MAAmB,GAAIyC,MAAM,CAC9BzC,MAAM;EACT,IAAIA,MAAM,KAAKjC,SAAS,EAAE;IACxB,MAAM2E,YAAY,GAAGL,eAAe,CAAC,QAAQ,CAAC;IAC9CrC,MAAM,GAAG0C,YAAY;IACrB;IACA;IACA;IACA;IACA,IAAI,OAAO1C,MAAM,KAAK,QAAQ,EAAE;MAC9B,MAAM2C,WAAW,GAAGN,eAAe,CAAC,eAAe,CAAC;MACpDrC,MAAM,GAAG2C,WAAW,CAAC3C,MAAM;IAC7B;IACA,IAAIA,MAAM,KAAKjC,SAAS,IAAIiC,MAAM,CAAC4C,MAAM,KAAK7E,SAAS,EAAE;MACvDiC,MAAM,GAAG0C,YAAY,CAACG,OAAO;IAC/B;EACF;EAEA,MAAMC,eAAe,GAAG;IACtB,GAAGvF,sBAAsB;IACzB,GAAGiF;EACL,CAAC;EACDrB,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,GAAGyB,eAAe,CAACtF,GAAG,CAAC;EAElDwC,MAAM,CAAC4C,MAAM,CAAC;IACZG,mBAAmBA,CACjBxE,SAG4B,EAC5BuC,aAA6D,EAC7D;MACA,OAAOD,YAAY,CAACtC,SAAS,EAAEuC,aAAa,CAAC;IAC/C;EACF,CAAC,CAAC;EAEFd,MAAM,CAAC4C,MAAM,CAAC;IACZI,mBAAmBA,CACjBzE,SAG4B,EAC5B0C,aAA2B,EAC3BC,MAAiC,GAAG,CAAC,CAAC,EACtC;MACA,OAAOF,YAAY,CAACzC,SAAS,EAAE0C,aAAa,EAAEC,MAAM,CAAC;IACvD;EACF,CAAC,CAAC;AACJ,CAAC;AAWD,OAAO,MAAM+B,gBAAgB,GAAI1E,SAA4B,IAAK;EAChE,OAAOI,eAAe;EACpB;EACA;EACAJ,SACF,CAAC;AACH,CAAC", "ignoreList": []}