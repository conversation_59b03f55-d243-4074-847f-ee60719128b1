{"version": 3, "names": ["with<PERSON><PERSON><PERSON>", "withTiming", "assertEasingIsWorklet", "BaseAnimationBuilder", "ComplexAnimationBuilder", "easing", "easingFunction", "instance", "createInstance", "__DEV__", "easingV", "rotate", "degree", "rotateV", "springify", "duration", "durationV", "type", "dampingRatio", "value", "dampingRatioV", "damping", "dampingV", "mass", "massV", "stiffness", "stiffnessV", "overshootClamping", "overshootClampingV", "restDisplacementThreshold", "restDisplacementThresholdV", "restSpeedThreshold", "restSpeedThresholdV", "withInitialValues", "values", "initialValues", "getAnimationAndConfig", "animation", "config", "maybeSetConfigValue", "variableName", "for<PERSON>ach"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/animationBuilder/ComplexAnimationBuilder.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,UAAU,EAAEC,UAAU,QAAQ,0BAAiB;AACxD,SAASC,qBAAqB,QAAQ,yBAAsB;AAS5D,SAASC,oBAAoB,QAAQ,2BAAwB;AAE7D,OAAO,MAAMC,uBAAuB,SAASD,oBAAoB,CAAC;EAiBhE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOE,MAAMA,CAEXC,cAAsD,EACtD;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACF,MAAM,CAACC,cAAc,CAAC;EACxC;EAEAD,MAAMA,CAACC,cAAsD,EAAQ;IACnE,IAAIG,OAAO,EAAE;MACXP,qBAAqB,CAACI,cAAc,CAAC;IACvC;IACA,IAAI,CAACI,OAAO,GAAGJ,cAAc;IAC7B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOK,MAAMA,CAEXC,MAAc,EACd;IACA,MAAML,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACI,MAAM,CAACC,MAAM,CAAC;EAChC;EAEAD,MAAMA,CAACC,MAAc,EAAQ;IAC3B,IAAI,CAACC,OAAO,GAAGD,MAAM;IACrB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOE,SAASA,CAEdC,QAAiB,EACQ;IACzB,MAAMR,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACO,SAAS,CAACC,QAAQ,CAAC;EACrC;EAEAD,SAASA,CAACC,QAAiB,EAAQ;IACjC,IAAI,CAACC,SAAS,GAAGD,QAAQ;IACzB,IAAI,CAACE,IAAI,GAAGjB,UAA+B;IAC3C,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOkB,YAAYA,CAEjBA,YAAoB,EACpB;IACA,MAAMX,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACW,YAAY,CAACA,YAAY,CAAC;EAC5C;EAEAA,YAAYA,CAACC,KAAa,EAAQ;IAChC,IAAI,CAACC,aAAa,GAAGD,KAAK;IAC1B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOE,OAAOA,CAEZA,OAAe,EACf;IACA,MAAMd,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACc,OAAO,CAACA,OAAO,CAAC;EAClC;EAEAA,OAAOA,CAACA,OAAe,EAAQ;IAC7B,IAAI,CAACC,QAAQ,GAAGD,OAAO;IACvB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOE,IAAIA,CAAoDA,IAAY,EAAE;IAC3E,MAAMhB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACgB,IAAI,CAACA,IAAI,CAAC;EAC5B;EAEAA,IAAIA,CAACA,IAAY,EAAQ;IACvB,IAAI,CAACC,KAAK,GAAGD,IAAI;IACjB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOE,SAASA,CAEdA,SAAiB,EACjB;IACA,MAAMlB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACkB,SAAS,CAACA,SAAS,CAAC;EACtC;EAEAA,SAASA,CAACA,SAAiB,EAAQ;IACjC,IAAI,CAACC,UAAU,GAAGD,SAAS;IAC3B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOE,iBAAiBA,CAEtBA,iBAAyB,EACzB;IACA,MAAMpB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACoB,iBAAiB,CAACA,iBAAiB,CAAC;EACtD;EAEAA,iBAAiBA,CAACA,iBAAyB,EAAQ;IACjD,IAAI,CAACC,kBAAkB,GAAGD,iBAAiB;IAC3C,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOE,yBAAyBA,CAE9BA,yBAAiC,EACjC;IACA,MAAMtB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACsB,yBAAyB,CAACA,yBAAyB,CAAC;EACtE;EAEAA,yBAAyBA,CAACA,yBAAiC,EAAE;IAC3D,IAAI,CAACC,0BAA0B,GAAGD,yBAAyB;IAC3D,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOE,kBAAkBA,CAEvBA,kBAA0B,EAC1B;IACA,MAAMxB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACwB,kBAAkB,CAACA,kBAAkB,CAAC;EACxD;EAEAA,kBAAkBA,CAACA,kBAA0B,EAAQ;IACnD,IAAI,CAACC,mBAAmB,GAAGD,kBAAkB;IAC7C,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOE,iBAAiBA,CAEtBC,MAAkB,EAClB;IACA,MAAM3B,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAAC0B,iBAAiB,CAACC,MAAM,CAAC;EAC3C;EAEAD,iBAAiBA,CAACC,MAAkB,EAAQ;IAC1C,IAAI,CAACC,aAAa,GAAGD,MAAM;IAC3B,OAAO,IAAI;EACb;EAEAE,qBAAqBA,CAAA,EAA6B;IAChD,MAAMrB,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMX,MAAM,GAAG,IAAI,CAACK,OAAO;IAC3B,MAAMC,MAAM,GAAG,IAAI,CAACE,OAAO;IAC3B,MAAMI,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,IAAI,CAACA,IAAI,GAAIhB,UAAgC;IACtE,MAAMoB,OAAO,GAAG,IAAI,CAACC,QAAQ;IAC7B,MAAMJ,YAAY,GAAG,IAAI,CAACE,aAAa;IACvC,MAAMG,IAAI,GAAG,IAAI,CAACC,KAAK;IACvB,MAAMC,SAAS,GAAG,IAAI,CAACC,UAAU;IACjC,MAAMC,iBAAiB,GAAG,IAAI,CAACC,kBAAkB;IACjD,MAAMC,yBAAyB,GAAG,IAAI,CAACC,0BAA0B;IACjE,MAAMC,kBAAkB,GAAG,IAAI,CAACC,mBAAmB;IAEnD,MAAMK,SAAS,GAAGpB,IAAI;IAEtB,MAAMqB,MAAkC,GAAG,CAAC,CAAC;IAE7C,SAASC,mBAAmBA,CAC1BpB,KAAsC,EACtCqB,YAAiB,EACjB;MACA,IAAIrB,KAAK,EAAE;QACTmB,MAAM,CAACE,YAAY,CAAC,GAAGrB,KAAK;MAC9B;IACF;IAEA,IAAIF,IAAI,KAAKhB,UAAU,EAAE;MACvBsC,mBAAmB,CAAClC,MAAM,EAAE,QAAQ,CAAC;IACvC;IAGE,CACE;MAAEmC,YAAY,EAAE,SAAS;MAAErB,KAAK,EAAEE;IAAQ,CAAC,EAC3C;MAAEmB,YAAY,EAAE,cAAc;MAAErB,KAAK,EAAED;IAAa,CAAC,EACrD;MAAEsB,YAAY,EAAE,MAAM;MAAErB,KAAK,EAAEI;IAAK,CAAC,EACrC;MAAEiB,YAAY,EAAE,WAAW;MAAErB,KAAK,EAAEM;IAAU,CAAC,EAC/C;MAAEe,YAAY,EAAE,mBAAmB;MAAErB,KAAK,EAAEQ;IAAkB,CAAC,EAC/D;MACEa,YAAY,EAAE,2BAA2B;MACzCrB,KAAK,EAAEU;IACT,CAAC,EACD;MAAEW,YAAY,EAAE,oBAAoB;MAAErB,KAAK,EAAEY;IAAmB,CAAC,EACjE;MAAES,YAAY,EAAE,UAAU;MAAErB,KAAK,EAAEJ;IAAS,CAAC,EAC7C;MAAEyB,YAAY,EAAE,QAAQ;MAAErB,KAAK,EAAER;IAAO,CAAC,CAC1C,CACD8B,OAAO,CAAC,CAAC;MAAEtB,KAAK;MAAEqB;IAAa,CAAC,KAChCD,mBAAmB,CAACpB,KAAK,EAAEqB,YAAY,CACzC,CAAC;IAED,OAAO,CAACH,SAAS,EAAEC,MAAM,CAAC;EAC5B;AACF", "ignoreList": []}