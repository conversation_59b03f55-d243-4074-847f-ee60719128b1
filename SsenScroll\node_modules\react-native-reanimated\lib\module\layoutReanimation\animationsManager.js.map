{"version": 3, "names": ["withStyleAnimation", "LayoutAnimationType", "makeMutableUI", "runOnUIImmediately", "TAG_OFFSET", "startObservingProgress", "tag", "sharedValue", "animationType", "isSharedTransition", "SHARED_ELEMENT_TRANSITION", "addListener", "global", "_notifyAboutProgress", "value", "stopObservingProgress", "<PERSON><PERSON><PERSON><PERSON>", "removeListener", "_notifyAboutEnd", "createLayoutAnimationManager", "currentAnimationForTag", "Map", "mutableValuesForTag", "start", "type", "yoga<PERSON><PERSON><PERSON>", "config", "SHARED_ELEMENT_TRANSITION_PROGRESS", "ProgressTransitionRegister", "onTransitionStart", "style", "currentAnimation", "animations", "previousAnimation", "get", "set", "undefined", "initialValues", "_value", "animation", "callback", "finished", "delete", "shouldRemoveView", "EXITING", "stop", "LayoutAnimationsManager"], "sourceRoot": "../../../src", "sources": ["layoutReanimation/animationsManager.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,kBAAkB,QAAQ,gCAA6B;AAOhE,SAASC,mBAAmB,QAAQ,mBAAgB;AACpD,SAASC,aAAa,QAAQ,gBAAa;AAC3C,SAASC,kBAAkB,QAAQ,eAAY;AAE/C,MAAMC,UAAU,GAAG,GAAG;AAEtB,SAASC,sBAAsBA,CAC7BC,GAAW,EACXC,WAAiD,EACjDC,aAAkC,EAC5B;EACN,SAAS;;EACT,MAAMC,kBAAkB,GACtBD,aAAa,KAAKP,mBAAmB,CAACS,yBAAyB;EACjEH,WAAW,CAACI,WAAW,CAACL,GAAG,GAAGF,UAAU,EAAE,MAAM;IAC9CQ,MAAM,CAACC,oBAAoB,CAACP,GAAG,EAAEC,WAAW,CAACO,KAAK,EAAEL,kBAAkB,CAAC;EACzE,CAAC,CAAC;AACJ;AAEA,SAASM,qBAAqBA,CAC5BT,GAAW,EACXC,WAAgC,EAChCS,UAAU,GAAG,KAAK,EACZ;EACN,SAAS;;EACTT,WAAW,CAACU,cAAc,CAACX,GAAG,GAAGF,UAAU,CAAC;EAC5CQ,MAAM,CAACM,eAAe,CAACZ,GAAG,EAAEU,UAAU,CAAC;AACzC;AAEA,SAASG,4BAA4BA,CAAA,EAGnC;EACA,SAAS;;EACT,MAAMC,sBAAsB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACxC,MAAMC,mBAAmB,GAAG,IAAID,GAAG,CAAC,CAAC;EAErC,OAAO;IACLE,KAAKA,CACHjB,GAAW,EACXkB,IAAyB;IACzB;AACN;AACA;AACA;IACMC,UAAqD,EACrDC,MAEoB,EACpB;MACA,IAAIF,IAAI,KAAKvB,mBAAmB,CAAC0B,kCAAkC,EAAE;QACnEf,MAAM,CAACgB,0BAA0B,CAACC,iBAAiB,CAACvB,GAAG,EAAEmB,UAAU,CAAC;QACpE;MACF;MAEA,MAAMK,KAAK,GAAGJ,MAAM,CAACD,UAAU,CAAC;MAChC,IAAIM,gBAAgB,GAAGD,KAAK,CAACE,UAAU;;MAEvC;MACA;MACA,MAAMC,iBAAiB,GAAGb,sBAAsB,CAACc,GAAG,CAAC5B,GAAG,CAAC;MACzD,IAAI2B,iBAAiB,EAAE;QACrBF,gBAAgB,GAAG;UAAE,GAAGE,iBAAiB;UAAE,GAAGH,KAAK,CAACE;QAAW,CAAC;MAClE;MACAZ,sBAAsB,CAACe,GAAG,CAAC7B,GAAG,EAAEyB,gBAAgB,CAAC;MAEjD,IAAIjB,KAAK,GAAGQ,mBAAmB,CAACY,GAAG,CAAC5B,GAAG,CAAC;MACxC,IAAIQ,KAAK,KAAKsB,SAAS,EAAE;QACvBtB,KAAK,GAAGZ,aAAa,CAAC4B,KAAK,CAACO,aAAa,CAAC;QAC1Cf,mBAAmB,CAACa,GAAG,CAAC7B,GAAG,EAAEQ,KAAK,CAAC;MACrC,CAAC,MAAM;QACLC,qBAAqB,CAACT,GAAG,EAAEQ,KAAK,CAAC;QACjCA,KAAK,CAACwB,MAAM,GAAGR,KAAK,CAACO,aAAa;MACpC;;MAEA;MACA,MAAME,SAAS,GAAGvC,kBAAkB,CAAC+B,gBAAgB,CAAC;MAEtDQ,SAAS,CAACC,QAAQ,GAAIC,QAAkB,IAAK;QAC3C,IAAIA,QAAQ,EAAE;UACZrB,sBAAsB,CAACsB,MAAM,CAACpC,GAAG,CAAC;UAClCgB,mBAAmB,CAACoB,MAAM,CAACpC,GAAG,CAAC;UAC/B,MAAMqC,gBAAgB,GAAGnB,IAAI,KAAKvB,mBAAmB,CAAC2C,OAAO;UAC7D7B,qBAAqB,CAACT,GAAG,EAAEQ,KAAK,EAAE6B,gBAAgB,CAAC;QACrD;QACAb,KAAK,CAACU,QAAQ,IACZV,KAAK,CAACU,QAAQ,CAACC,QAAQ,KAAKL,SAAS,GAAG,KAAK,GAAGK,QAAQ,CAAC;MAC7D,CAAC;MAEDpC,sBAAsB,CAACC,GAAG,EAAEQ,KAAK,EAAEU,IAAI,CAAC;MACxCV,KAAK,CAACA,KAAK,GAAGyB,SAAS;IACzB,CAAC;IACDM,IAAIA,CAACvC,GAAW,EAAE;MAChB,MAAMQ,KAAK,GAAGQ,mBAAmB,CAACY,GAAG,CAAC5B,GAAG,CAAC;MAC1C,IAAI,CAACQ,KAAK,EAAE;QACV;MACF;MACAC,qBAAqB,CAACT,GAAG,EAAEQ,KAAK,CAAC;IACnC;EACF,CAAC;AACH;AAEAX,kBAAkB,CAAC,MAAM;EACvB,SAAS;;EACTS,MAAM,CAACkC,uBAAuB,GAAG3B,4BAA4B,CAAC,CAAC;AACjE,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}