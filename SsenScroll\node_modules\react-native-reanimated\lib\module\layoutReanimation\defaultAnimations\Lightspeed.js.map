{"version": 3, "names": ["withSequence", "withTiming", "ComplexAnimationBuilder", "LightSpeedInRight", "presetName", "createInstance", "build", "delayFunction", "getDelayFunction", "animation", "config", "getAnimationAndConfig", "delay", "get<PERSON>elay", "duration", "getDuration", "callback", "callbackV", "initialValues", "values", "animations", "opacity", "transform", "translateX", "skewX", "windowWidth", "LightSpeedInLeft", "LightSpeedOutRight", "LightSpeedOutLeft"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/defaultAnimations/Lightspeed.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,YAAY,EAAEC,UAAU,QAAQ,0BAAiB;AAO1D,SAASC,uBAAuB,QAAQ,8BAAqB;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,SACpBD,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,mBAAmB;EAEvC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIF,iBAAiB,CAAC,CAAC;EAChC;EAEAG,KAAK,GAAGA,CAAA,KAAkC;IACxC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IACnC,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAiC,IAAK;MAC5C,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEd,aAAa,CAACK,KAAK,EAAEX,UAAU,CAAC,CAAC,EAAE;YAAEa;UAAS,CAAC,CAAC,CAAC;UAC1DQ,SAAS,EAAE,CACT;YACEC,UAAU,EAAEhB,aAAa,CACvBK,KAAK,EACLH,SAAS,CAAC,CAAC,EAAE;cAAE,GAAGC,MAAM;cAAEI,QAAQ,EAAEA,QAAQ,GAAG;YAAI,CAAC,CACtD;UACF,CAAC,EACD;YACEU,KAAK,EAAEjB,aAAa,CAClBK,KAAK,EACLZ,YAAY,CACVC,UAAU,CAAC,OAAO,EAAE;cAAEa,QAAQ,EAAEA,QAAQ,GAAG;YAAI,CAAC,CAAC,EACjDb,UAAU,CAAC,OAAO,EAAE;cAAEa,QAAQ,EAAEA,QAAQ,GAAG;YAAK,CAAC,CAAC,EAClDb,UAAU,CAAC,MAAM,EAAE;cAAEa,QAAQ,EAAEA,QAAQ,GAAG;YAAK,CAAC,CAClD,CACF;UACF,CAAC;QAEL,CAAC;QACDI,aAAa,EAAE;UACbG,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,CAAC;YAAEC,UAAU,EAAEJ,MAAM,CAACM;UAAY,CAAC,EAAE;YAAED,KAAK,EAAE;UAAS,CAAC,CAAC;UACpE,GAAGN;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMU,gBAAgB,SACnBxB,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,kBAAkB;EAEtC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIqB,gBAAgB,CAAC,CAAC;EAC/B;EAEApB,KAAK,GAAGA,CAAA,KAAkC;IACxC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IACnC,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAiC,IAAK;MAC5C,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEd,aAAa,CAACK,KAAK,EAAEX,UAAU,CAAC,CAAC,EAAE;YAAEa;UAAS,CAAC,CAAC,CAAC;UAC1DQ,SAAS,EAAE,CACT;YACEC,UAAU,EAAEhB,aAAa,CACvBK,KAAK,EACLH,SAAS,CAAC,CAAC,EAAE;cAAE,GAAGC,MAAM;cAAEI,QAAQ,EAAEA,QAAQ,GAAG;YAAI,CAAC,CACtD;UACF,CAAC,EACD;YACEU,KAAK,EAAEjB,aAAa,CAClBK,KAAK,EACLZ,YAAY,CACVC,UAAU,CAAC,QAAQ,EAAE;cAAEa,QAAQ,EAAEA,QAAQ,GAAG;YAAI,CAAC,CAAC,EAClDb,UAAU,CAAC,MAAM,EAAE;cAAEa,QAAQ,EAAEA,QAAQ,GAAG;YAAK,CAAC,CAAC,EACjDb,UAAU,CAAC,MAAM,EAAE;cAAEa,QAAQ,EAAEA,QAAQ,GAAG;YAAK,CAAC,CAClD,CACF;UACF,CAAC;QAEL,CAAC;QACDI,aAAa,EAAE;UACbG,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,CAAC;YAAEC,UAAU,EAAE,CAACJ,MAAM,CAACM;UAAY,CAAC,EAAE;YAAED,KAAK,EAAE;UAAQ,CAAC,CAAC;UACpE,GAAGN;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,kBAAkB,SACrBzB,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,oBAAoB;EAExC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIsB,kBAAkB,CAAC,CAAC;EACjC;EAEArB,KAAK,GAAGA,CAAA,KAAkC;IACxC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMG,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAiC,IAAK;MAC5C,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;UACnDY,SAAS,EAAE,CACT;YACEC,UAAU,EAAEhB,aAAa,CACvBK,KAAK,EACLH,SAAS,CAACU,MAAM,CAACM,WAAW,EAAEf,MAAM,CACtC;UACF,CAAC,EACD;YACEc,KAAK,EAAEjB,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,QAAQ,EAAEC,MAAM,CAAC;UACzD,CAAC;QAEL,CAAC;QACDQ,aAAa,EAAE;UACbG,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,CAAC;YAAEC,UAAU,EAAE;UAAE,CAAC,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAC,CAAC;UACjD,GAAGN;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMY,iBAAiB,SACpB1B,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,mBAAmB;EAEvC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIuB,iBAAiB,CAAC,CAAC;EAChC;EAEAtB,KAAK,GAAGA,CAAA,KAAkC;IACxC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMG,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAiC,IAAK;MAC5C,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;UACnDY,SAAS,EAAE,CACT;YACEC,UAAU,EAAEhB,aAAa,CACvBK,KAAK,EACLH,SAAS,CAAC,CAACU,MAAM,CAACM,WAAW,EAAEf,MAAM,CACvC;UACF,CAAC,EACD;YACEc,KAAK,EAAEjB,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,OAAO,EAAEC,MAAM,CAAC;UACxD,CAAC;QAEL,CAAC;QACDQ,aAAa,EAAE;UACbG,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,CAAC;YAAEC,UAAU,EAAE;UAAE,CAAC,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAC,CAAC;UACjD,GAAGN;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH", "ignoreList": []}