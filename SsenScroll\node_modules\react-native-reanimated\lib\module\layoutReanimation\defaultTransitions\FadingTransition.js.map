{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "withSequence", "withTiming", "BaseAnimationBuilder", "FadingTransition", "presetName", "createInstance", "build", "delayFunction", "getDelayFunction", "callback", "callbackV", "delay", "get<PERSON>elay", "halfDuration", "durationV", "values", "initialValues", "opacity", "originX", "currentOriginX", "originY", "currentOriginY", "width", "currentWidth", "height", "currentHeight", "animations", "duration", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/defaultTransitions/FadingTransition.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,YAAY,EAAEC,UAAU,QAAQ,0BAAiB;AAKrE,SAASC,oBAAoB,QAAQ,8BAAqB;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,SACnBD,oBAAoB,CAE9B;EACE,OAAOE,UAAU,GAAG,kBAAkB;EAEtC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIF,gBAAgB,CAAC,CAAC;EAC/B;EAEAG,KAAK,GAAGA,CAAA,KAA+B;IACrC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,YAAY,GAAG,CAAC,IAAI,CAACC,SAAS,IAAI,GAAG,IAAI,CAAC;IAEhD,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,aAAa,EAAE;UACbC,OAAO,EAAE,CAAC;UACVC,OAAO,EAAEH,MAAM,CAACI,cAAc;UAC9BC,OAAO,EAAEL,MAAM,CAACM,cAAc;UAC9BC,KAAK,EAAEP,MAAM,CAACQ,YAAY;UAC1BC,MAAM,EAAET,MAAM,CAACU;QACjB,CAAC;QACDC,UAAU,EAAE;UACVT,OAAO,EAAEV,aAAa,CACpBI,KAAK,EACLX,YAAY,CACVC,UAAU,CAAC,CAAC,EAAE;YAAE0B,QAAQ,EAAEd;UAAa,CAAC,CAAC,EACzCZ,UAAU,CAAC,CAAC,EAAE;YAAE0B,QAAQ,EAAEd;UAAa,CAAC,CAC1C,CACF,CAAC;UACDK,OAAO,EAAEnB,SAAS,CAChBY,KAAK,GAAGE,YAAY,EACpBZ,UAAU,CAACc,MAAM,CAACa,aAAa,EAAE;YAAED,QAAQ,EAAE;UAAE,CAAC,CAClD,CAAC;UACDP,OAAO,EAAErB,SAAS,CAChBY,KAAK,GAAGE,YAAY,EACpBZ,UAAU,CAACc,MAAM,CAACc,aAAa,EAAE;YAAEF,QAAQ,EAAE;UAAE,CAAC,CAClD,CAAC;UACDL,KAAK,EAAEvB,SAAS,CACdY,KAAK,GAAGE,YAAY,EACpBZ,UAAU,CAACc,MAAM,CAACe,WAAW,EAAE;YAAEH,QAAQ,EAAE;UAAE,CAAC,CAChD,CAAC;UACDH,MAAM,EAAEzB,SAAS,CACfY,KAAK,GAAGE,YAAY,EACpBZ,UAAU,CAACc,MAAM,CAACgB,YAAY,EAAE;YAAEJ,QAAQ,EAAE;UAAE,CAAC,CACjD;QACF,CAAC;QACDlB;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH", "ignoreList": []}