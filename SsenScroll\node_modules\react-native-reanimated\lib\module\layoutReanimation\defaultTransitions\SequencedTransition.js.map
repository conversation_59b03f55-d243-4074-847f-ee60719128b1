{"version": 3, "names": ["withSequence", "withTiming", "BaseAnimationBuilder", "SequencedTransition", "presetName", "reversed", "createInstance", "reverse", "instance", "build", "delayFunction", "getDelayFunction", "callback", "callbackV", "delay", "get<PERSON>elay", "halfDuration", "durationV", "config", "duration", "values", "initialValues", "originX", "currentOriginX", "originY", "currentOriginY", "width", "currentWidth", "height", "currentHeight", "animations", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/defaultTransitions/SequencedTransition.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,YAAY,EAAEC,UAAU,QAAQ,0BAAiB;AAK1D,SAASC,oBAAoB,QAAQ,8BAAqB;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,SACtBD,oBAAoB,CAE9B;EACE,OAAOE,UAAU,GAAG,qBAAqB;EAEzCC,QAAQ,GAAG,KAAK;EAEhB,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIH,mBAAmB,CAAC,CAAC;EAClC;EAEA,OAAOI,OAAOA,CAAA,EAAwB;IACpC,MAAMC,QAAQ,GAAGL,mBAAmB,CAACG,cAAc,CAAC,CAAC;IACrD,OAAOE,QAAQ,CAACD,OAAO,CAAC,CAAC;EAC3B;EAEAA,OAAOA,CAAA,EAAwB;IAC7B,IAAI,CAACF,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;IAC9B,OAAO,IAAI;EACb;EAEAI,KAAK,GAAGA,CAAA,KAA+B;IACrC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,YAAY,GAAG,CAAC,IAAI,CAACC,SAAS,IAAI,GAAG,IAAI,CAAC;IAChD,MAAMC,MAAM,GAAG;MAAEC,QAAQ,EAAEH;IAAa,CAAC;IACzC,MAAMT,OAAO,GAAG,IAAI,CAACF,QAAQ;IAE7B,OAAQe,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,aAAa,EAAE;UACbC,OAAO,EAAEF,MAAM,CAACG,cAAc;UAC9BC,OAAO,EAAEJ,MAAM,CAACK,cAAc;UAC9BC,KAAK,EAAEN,MAAM,CAACO,YAAY;UAC1BC,MAAM,EAAER,MAAM,CAACS;QACjB,CAAC;QACDC,UAAU,EAAE;UACVR,OAAO,EAAEZ,aAAa,CACpBI,KAAK,EACLd,YAAY,CACVC,UAAU,CACRM,OAAO,GAAGa,MAAM,CAACG,cAAc,GAAGH,MAAM,CAACW,aAAa,EACtDb,MACF,CAAC,EACDjB,UAAU,CAACmB,MAAM,CAACW,aAAa,EAAEb,MAAM,CACzC,CACF,CAAC;UACDM,OAAO,EAAEd,aAAa,CACpBI,KAAK,EACLd,YAAY,CACVC,UAAU,CACRM,OAAO,GAAGa,MAAM,CAACY,aAAa,GAAGZ,MAAM,CAACK,cAAc,EACtDP,MACF,CAAC,EACDjB,UAAU,CAACmB,MAAM,CAACY,aAAa,EAAEd,MAAM,CACzC,CACF,CAAC;UACDQ,KAAK,EAAEhB,aAAa,CAClBI,KAAK,EACLd,YAAY,CACVC,UAAU,CACRM,OAAO,GAAGa,MAAM,CAACO,YAAY,GAAGP,MAAM,CAACa,WAAW,EAClDf,MACF,CAAC,EACDjB,UAAU,CAACmB,MAAM,CAACa,WAAW,EAAEf,MAAM,CACvC,CACF,CAAC;UACDU,MAAM,EAAElB,aAAa,CACnBI,KAAK,EACLd,YAAY,CACVC,UAAU,CACRM,OAAO,GAAGa,MAAM,CAACc,YAAY,GAAGd,MAAM,CAACS,aAAa,EACpDX,MACF,CAAC,EACDjB,UAAU,CAACmB,MAAM,CAACc,YAAY,EAAEhB,MAAM,CACxC,CACF;QACF,CAAC;QACDN;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH", "ignoreList": []}