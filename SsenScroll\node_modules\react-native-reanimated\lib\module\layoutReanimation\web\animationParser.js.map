{"version": 3, "names": ["WebEasings", "convertAnimationObjectToKeyframes", "animationObject", "keyframe", "name", "timestamp", "style", "Object", "entries", "step", "property", "values", "easingName", "toString", "for<PERSON>ach", "value", "transformProperty", "transformPropertyValue"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/web/animationParser.ts"], "mappings": "AAAA,YAAY;;AAGZ,SAASA,UAAU,QAAQ,iBAAc;AAuCzC,OAAO,SAASC,iCAAiCA,CAC/CC,eAA8B,EAC9B;EACA,IAAIC,QAAQ,GAAG,cAAcD,eAAe,CAACE,IAAI,KAAK;EAEtD,KAAK,MAAM,CAACC,SAAS,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACN,eAAe,CAACI,KAAK,CAAC,EAAE;IACtE,MAAMG,IAAI,GACRJ,SAAS,KAAK,MAAM,GAAG,CAAC,GAAGA,SAAS,KAAK,IAAI,GAAG,GAAG,GAAGA,SAAS;IAEjEF,QAAQ,IAAI,GAAGM,IAAI,MAAM;IAEzB,KAAK,MAAM,CAACC,QAAQ,EAAEC,MAAM,CAAC,IAAIJ,MAAM,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;MACtD,IAAII,QAAQ,KAAK,QAAQ,EAAE;QACzB,IAAIE,UAA2B,GAAG,QAAQ;QAE1C,IAAID,MAAM,IAAIX,UAAU,EAAE;UACxBY,UAAU,GAAGD,MAAM;QACrB,CAAC,MAAM,IAAIA,MAAM,CAACP,IAAI,IAAIJ,UAAU,EAAE;UACpCY,UAAU,GAAGD,MAAM,CAACP,IAAI;QAC1B;QAEAD,QAAQ,IAAI,2CAA2CH,UAAU,CAC/DY,UAAU,CACX,CAACC,QAAQ,CAAC,CAAC,IAAI;QAEhB;MACF;MAEA,IAAIH,QAAQ,KAAK,SAAS,EAAE;QAC1BP,QAAQ,IAAI,SAASQ,MAAM,MAAM;QACjC;MACF;MAEA,IAAID,QAAQ,KAAK,SAAS,EAAE;QAC1BP,QAAQ,IAAI,QAAQQ,MAAM,MAAM;QAChC;MACF;MAEA,IAAID,QAAQ,KAAK,WAAW,EAAE;QAC5BP,QAAQ,IAAI,GAAGO,QAAQ,KAAKC,MAAM,IAAI;QACtC;MACF;MAEAR,QAAQ,IAAI,YAAY;MAExBQ,MAAM,CAACG,OAAO,CAAEC,KAAuC,IAAK;QAC1D,KAAK,MAAM,CACTC,iBAAiB,EACjBC,sBAAsB,CACvB,IAAIV,MAAM,CAACC,OAAO,CAACO,KAAK,CAAC,EAAE;UAC1BZ,QAAQ,IAAI,IAAIa,iBAAiB,IAAIC,sBAAsB,GAAG;QAChE;MACF,CAAC,CAAC;MACFd,QAAQ,IAAI,IAAI,CAAC,CAAC;IACpB;IACAA,QAAQ,IAAI,IAAI,CAAC,CAAC;EACpB;EACAA,QAAQ,IAAI,IAAI,CAAC,CAAC;;EAElB,OAAOA,QAAQ;AACjB", "ignoreList": []}