{"version": 3, "names": ["LayoutAnimationType", "ReduceMotion", "EasingNameSymbol", "logger", "_updatePropsJS", "ReducedMotionManager", "Keyframe", "setElementPosition", "snapshots", "Animations", "TransitionType", "TransitionGenerator", "scheduleAnimationCleanup", "getEasingByName", "WebEasings", "prepareCurvedTransition", "getEasingFromConfig", "config", "easingV", "easingName", "warn", "getRandomDelay", "max<PERSON><PERSON><PERSON>", "Math", "floor", "random", "getDelayFromConfig", "shouldRandomizeDelay", "randomizeDelay", "delay", "delayV", "getReducedMotionFromConfig", "reduceMotionV", "jsValue", "Never", "Always", "getDurationFromConfig", "animationName", "defaultDuration", "duration", "durationV", "undefined", "getCallbackFromConfig", "callbackV", "getReversedFromConfig", "reversed", "getProcessedConfig", "animationType", "easing", "callback", "maybeModifyStyleForKeyframe", "element", "style", "animationFillMode", "timestampRules", "Object", "values", "definitions", "position", "saveSnapshot", "rect", "getBoundingClientRect", "snapshot", "top", "left", "width", "height", "scrollOffsets", "getElementScrollValue", "set", "setElementAnimation", "animationConfig", "shouldSavePosition", "parent", "configureAnimation", "animationDuration", "animationDelay", "animationTimingFunction", "ENTERING", "requestAnimationFrame", "onanimationend", "contains", "removedAfterAnimation", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "animationCancelHandler", "onanimationstart", "visibility", "addEventListener", "get", "handleLayoutTransition", "transitionData", "LINEAR", "SEQUENCED", "FADING", "JUMPING", "CURVED", "ENTRY_EXIT", "transitionKeyframeName", "dummyTransitionKeyframeName", "dummy", "dummyAnimationConfig", "current", "scrollTopOffset", "scrollLeftOffset", "scrollTop", "scrollLeft", "parentElement", "handleExitingAnimation", "offsetParent", "cloneNode", "reanimatedDummy", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "currentScrollTopOffset", "lastScrollTopOffset", "currentScrollLeftOffset", "lastScrollLeftOffset"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/web/componentUtils.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,mBAAmB,EAAEC,YAAY,QAAQ,sBAAmB;AACrE,SAASC,gBAAgB,QAAQ,iBAAc;AAC/C,SAASC,MAAM,QAAQ,uBAAc;AAErC,SAASC,cAAc,QAAQ,+CAAsC;AACrE,SAASC,oBAAoB,QAAQ,wBAAqB;AAC1D,SAASC,QAAQ,QAAQ,8BAAqB;AAG9C,SAASC,kBAAkB,EAAEC,SAAS,QAAQ,qBAAkB;AAQhE,SAASC,UAAU,EAAEC,cAAc,QAAQ,aAAU;AACrD,SAASC,mBAAmB,QAAQ,sBAAmB;AACvD,SAASC,wBAAwB,QAAQ,eAAY;AAErD,SAASC,eAAe,EAAEC,UAAU,QAAQ,iBAAc;AAC1D,SAASC,uBAAuB,QAAQ,4BAAyB;AAEjE,SAASC,mBAAmBA,CAACC,MAAoB,EAAU;EACzD,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;IACnB,OAAOL,eAAe,CAAC,QAAQ,CAAC;EAClC;EAEA,MAAMM,UAAU,GAAGF,MAAM,CAACC,OAAO,CAAChB,gBAAgB,CAAC;EAEnD,IAAI,EAAEiB,UAAU,IAAIL,UAAU,CAAC,EAAE;IAC/BX,MAAM,CAACiB,IAAI,CAAC,oDAAoD,CAAC;IAEjE,OAAOP,eAAe,CAAC,QAAQ,CAAC;EAClC;EAEA,OAAOA,eAAe,CAACM,UAA6B,CAAC;AACvD;AAEA,SAASE,cAAcA,CAACC,QAAQ,GAAG,IAAI,EAAE;EACvC,OAAOC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,IAAIH,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;AAC1D;AAEA,SAASI,kBAAkBA,CAACT,MAAoB,EAAU;EACxD,MAAMU,oBAAoB,GAAGV,MAAM,CAACW,cAAc;EAElD,MAAMC,KAAK,GAAGF,oBAAoB,GAAGN,cAAc,CAAC,CAAC,GAAG,CAAC;EAEzD,IAAI,CAACJ,MAAM,CAACa,MAAM,EAAE;IAClB,OAAOD,KAAK;EACd;EAEA,OAAOF,oBAAoB,GACvBN,cAAc,CAACJ,MAAM,CAACa,MAAM,CAAC,GAC7Bb,MAAM,CAACa,MAAM,GAAG,IAAI;AAC1B;AAEA,OAAO,SAASC,0BAA0BA,CAACd,MAAoB,EAAE;EAC/D,IAAI,CAACA,MAAM,CAACe,aAAa,EAAE;IACzB,OAAO3B,oBAAoB,CAAC4B,OAAO;EACrC;EAEA,QAAQhB,MAAM,CAACe,aAAa;IAC1B,KAAK/B,YAAY,CAACiC,KAAK;MACrB,OAAO,KAAK;IACd,KAAKjC,YAAY,CAACkC,MAAM;MACtB,OAAO,IAAI;IACb;MACE,OAAO9B,oBAAoB,CAAC4B,OAAO;EACvC;AACF;AAEA,SAASG,qBAAqBA,CAC5BnB,MAAoB,EACpBoB,aAAqB,EACb;EACR;EACA;;EAEA,MAAMC,eAAe,GACnBD,aAAa,IAAI5B,UAAU,GACvBA,UAAU,CAAC4B,aAAa,CAAmB,CAACE,QAAQ,GACpD,GAAG;EAET,OAAOtB,MAAM,CAACuB,SAAS,KAAKC,SAAS,GACjCxB,MAAM,CAACuB,SAAS,GAAG,IAAI,GACvBF,eAAe;AACrB;AAEA,SAASI,qBAAqBA,CAACzB,MAAoB,EAAqB;EACtE,OAAOA,MAAM,CAAC0B,SAAS,KAAKF,SAAS,GAAGxB,MAAM,CAAC0B,SAAS,GAAG,IAAI;AACjE;AAEA,SAASC,qBAAqBA,CAAC3B,MAAoB,EAAE;EACnD,OAAO,CAAC,CAACA,MAAM,CAAC4B,QAAQ;AAC1B;AAEA,OAAO,SAASC,kBAAkBA,CAChCT,aAAqB,EACrBU,aAAkC,EAClC9B,MAAoB,EACH;EACjB,OAAO;IACLoB,aAAa;IACbU,aAAa;IACbR,QAAQ,EAAEH,qBAAqB,CAACnB,MAAM,EAAEoB,aAAa,CAAC;IACtDR,KAAK,EAAEH,kBAAkB,CAACT,MAAM,CAAC;IACjC+B,MAAM,EAAEhC,mBAAmB,CAACC,MAAM,CAAC;IACnCgC,QAAQ,EAAEP,qBAAqB,CAACzB,MAAM,CAAC;IACvC4B,QAAQ,EAAED,qBAAqB,CAAC3B,MAAM;EACxC,CAAC;AACH;AAEA,OAAO,SAASiC,2BAA2BA,CACzCC,OAAoB,EACpBlC,MAAoB,EACpB;EACA,IAAI,EAAEA,MAAM,YAAYX,QAAQ,CAAC,EAAE;IACjC;EACF;;EAEA;EACA;EACA6C,OAAO,CAACC,KAAK,CAACC,iBAAiB,GAAG,UAAU;EAE5C,KAAK,MAAMC,cAAc,IAAIC,MAAM,CAACC,MAAM,CACxCvC,MAAM,CAACwC,WACT,CAAC,EAAE;IACD,IAAI,SAAS,IAAIH,cAAc,IAAI,SAAS,IAAIA,cAAc,EAAE;MAC9DH,OAAO,CAACC,KAAK,CAACM,QAAQ,GAAG,UAAU;MACnC;IACF;EACF;AACF;AAEA,OAAO,SAASC,YAAYA,CAACR,OAAoB,EAAE;EACjD,MAAMS,IAAI,GAAGT,OAAO,CAACU,qBAAqB,CAAC,CAAC;EAE5C,MAAMC,QAA4B,GAAG;IACnCC,GAAG,EAAEH,IAAI,CAACG,GAAG;IACbC,IAAI,EAAEJ,IAAI,CAACI,IAAI;IACfC,KAAK,EAAEL,IAAI,CAACK,KAAK;IACjBC,MAAM,EAAEN,IAAI,CAACM,MAAM;IACnBC,aAAa,EAAEC,qBAAqB,CAACjB,OAAO;EAC9C,CAAC;EAED3C,SAAS,CAAC6D,GAAG,CAAClB,OAAO,EAAEW,QAAQ,CAAC;AAClC;AAEA,OAAO,SAASQ,mBAAmBA,CACjCnB,OAA8B,EAC9BoB,eAAgC,EAChCC,kBAAkB,GAAG,KAAK,EAC1BC,MAAsB,GAAG,IAAI,EAC7B;EACA,MAAM;IAAEpC,aAAa;IAAEE,QAAQ;IAAEV,KAAK;IAAEmB;EAAO,CAAC,GAAGuB,eAAe;EAElE,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/BvB,OAAO,CAACC,KAAK,CAACf,aAAa,GAAGA,aAAa;IAC3Cc,OAAO,CAACC,KAAK,CAACuB,iBAAiB,GAAG,GAAGpC,QAAQ,GAAG;IAChDY,OAAO,CAACC,KAAK,CAACwB,cAAc,GAAG,GAAG/C,KAAK,GAAG;IAC1CsB,OAAO,CAACC,KAAK,CAACyB,uBAAuB,GAAG7B,MAAM;EAChD,CAAC;EAED,IAAIuB,eAAe,CAACxB,aAAa,KAAK/C,mBAAmB,CAAC8E,QAAQ,EAAE;IAClE;IACA;IACAC,qBAAqB,CAACL,kBAAkB,CAAC;EAC3C,CAAC,MAAM;IACLA,kBAAkB,CAAC,CAAC;EACtB;EAEAvB,OAAO,CAAC6B,cAAc,GAAG,MAAM;IAC7B,IAAIR,kBAAkB,EAAE;MACtBb,YAAY,CAACR,OAAO,CAAC;IACvB;IAEA,IAAIsB,MAAM,EAAEQ,QAAQ,CAAC9B,OAAO,CAAC,EAAE;MAC7BA,OAAO,CAAC+B,qBAAqB,GAAG,IAAI;MACpCT,MAAM,CAACU,WAAW,CAAChC,OAAO,CAAC;IAC7B;IAEAoB,eAAe,CAACtB,QAAQ,GAAG,IAAI,CAAC;IAChCE,OAAO,CAACiC,mBAAmB,CAAC,iBAAiB,EAAEC,sBAAsB,CAAC;EACxE,CAAC;EAED,MAAMA,sBAAsB,GAAGA,CAAA,KAAM;IACnCd,eAAe,CAACtB,QAAQ,GAAG,KAAK,CAAC;IAEjC,IAAIwB,MAAM,EAAEQ,QAAQ,CAAC9B,OAAO,CAAC,EAAE;MAC7BA,OAAO,CAAC+B,qBAAqB,GAAG,IAAI;MACpCT,MAAM,CAACU,WAAW,CAAChC,OAAO,CAAC;IAC7B;IAEAA,OAAO,CAACiC,mBAAmB,CAAC,iBAAiB,EAAEC,sBAAsB,CAAC;EACxE,CAAC;;EAED;EACAlC,OAAO,CAACmC,gBAAgB,GAAG,MAAM;IAC/B,IAAIf,eAAe,CAACxB,aAAa,KAAK/C,mBAAmB,CAAC8E,QAAQ,EAAE;MAClE1E,cAAc,CAAC;QAAEmF,UAAU,EAAE;MAAU,CAAC,EAAEpC,OAAO,CAAC;IACpD;IAEAA,OAAO,CAACqC,gBAAgB,CAAC,iBAAiB,EAAEH,sBAAsB,CAAC;EACrE,CAAC;EAED,IAAI,EAAEhD,aAAa,IAAI5B,UAAU,CAAC,EAAE;IAClCG,wBAAwB,CAACyB,aAAa,EAAEE,QAAQ,GAAGV,KAAK,EAAE,MAAM;MAC9D,IAAI2C,kBAAkB,EAAE;QACtBjE,kBAAkB,CAAC4C,OAAO,EAAE3C,SAAS,CAACiF,GAAG,CAACtC,OAAO,CAAE,CAAC;MACtD;IACF,CAAC,CAAC;EACJ;AACF;AAEA,OAAO,SAASuC,sBAAsBA,CACpCvC,OAA8B,EAC9BoB,eAAgC,EAChCoB,cAA8B,EAC9B;EACA,MAAM;IAAEtD;EAAc,CAAC,GAAGkC,eAAe;EAEzC,IAAIxB,aAAa;EAEjB,QAAQV,aAAa;IACnB,KAAK,kBAAkB;MACrBU,aAAa,GAAGrC,cAAc,CAACkF,MAAM;MACrC;IACF,KAAK,qBAAqB;MACxB7C,aAAa,GAAGrC,cAAc,CAACmF,SAAS;MACxC;IACF,KAAK,kBAAkB;MACrB9C,aAAa,GAAGrC,cAAc,CAACoF,MAAM;MACrC;IACF,KAAK,mBAAmB;MACtB/C,aAAa,GAAGrC,cAAc,CAACqF,OAAO;MACtC;IACF,KAAK,kBAAkB;MACrBhD,aAAa,GAAGrC,cAAc,CAACsF,MAAM;MACrC;IACF,KAAK,qBAAqB;MACxBjD,aAAa,GAAGrC,cAAc,CAACuF,UAAU;MACzC;IACF;MACElD,aAAa,GAAGrC,cAAc,CAACkF,MAAM;MACrC;EACJ;EAEA,MAAM;IAAEM,sBAAsB;IAAEC;EAA4B,CAAC,GAC3DxF,mBAAmB,CAACoC,aAAa,EAAE4C,cAAc,CAAC;EAEpDpB,eAAe,CAAClC,aAAa,GAAG6D,sBAAsB;EAEtD,IAAInD,aAAa,KAAKrC,cAAc,CAACsF,MAAM,EAAE;IAC3C,MAAM;MAAEI,KAAK;MAAEC;IAAqB,CAAC,GAAGtF,uBAAuB,CAC7DoC,OAAO,EACPoB,eAAe,EACfoB,cAAc,EACdQ,2BAA2B,CAAE;IAC/B,CAAC;IAED7B,mBAAmB,CAAC8B,KAAK,EAAEC,oBAAoB,CAAC;EAClD;EACA/B,mBAAmB,CAACnB,OAAO,EAAEoB,eAAe,CAAC;AAC/C;AAEA,SAASH,qBAAqBA,CAACjB,OAAoB,EAAiB;EAClE,IAAImD,OAA2B,GAAGnD,OAAO;EAEzC,MAAMgB,aAA4B,GAAG;IACnCoC,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE;EACpB,CAAC;EAED,OAAOF,OAAO,EAAE;IACd,IAAIA,OAAO,CAACG,SAAS,KAAK,CAAC,IAAItC,aAAa,CAACoC,eAAe,KAAK,CAAC,EAAE;MAClEpC,aAAa,CAACoC,eAAe,GAAGD,OAAO,CAACG,SAAS;IACnD;IAEA,IAAIH,OAAO,CAACI,UAAU,KAAK,CAAC,IAAIvC,aAAa,CAACqC,gBAAgB,KAAK,CAAC,EAAE;MACpErC,aAAa,CAACqC,gBAAgB,GAAGF,OAAO,CAACI,UAAU;IACrD;IAEAJ,OAAO,GAAGA,OAAO,CAACK,aAAa;EACjC;EAEA,OAAOxC,aAAa;AACtB;AAEA,OAAO,SAASyC,sBAAsBA,CACpCzD,OAAoB,EACpBoB,eAAgC,EAChC;EACA,MAAME,MAAM,GAAGtB,OAAO,CAAC0D,YAAY;EACnC,MAAMT,KAAK,GAAGjD,OAAO,CAAC2D,SAAS,CAAC,CAA0B;EAC1DV,KAAK,CAACW,eAAe,GAAG,IAAI;EAE5B5D,OAAO,CAACC,KAAK,CAACf,aAAa,GAAG,EAAE;EAChC+D,KAAK,CAAChD,KAAK,CAACf,aAAa,GAAG,EAAE;;EAE9B;EACA;EACA;EACA;EACA;EACA,OAAOc,OAAO,CAAC6D,UAAU,EAAE;IACzBZ,KAAK,CAACa,WAAW,CAAC9D,OAAO,CAAC6D,UAAU,CAAC;EACvC;EAEAvC,MAAM,EAAEwC,WAAW,CAACb,KAAK,CAAC;EAE1B,MAAMtC,QAAQ,GAAGtD,SAAS,CAACiF,GAAG,CAACtC,OAAO,CAAE;EAExC,MAAMgB,aAAa,GAAGC,qBAAqB,CAACjB,OAAO,CAAC;;EAEpD;EACA;EACA;EACA;;EAEA,MAAM+D,sBAAsB,GAAG/C,aAAa,CAACoC,eAAe;EAC5D,MAAMY,mBAAmB,GAAGrD,QAAQ,CAACK,aAAa,CAACoC,eAAe;EAElE,IAAIW,sBAAsB,KAAKC,mBAAmB,EAAE;IAClDrD,QAAQ,CAACC,GAAG,IAAIoD,mBAAmB,GAAGD,sBAAsB;EAC9D;EAEA,MAAME,uBAAuB,GAAGjD,aAAa,CAACqC,gBAAgB;EAC9D,MAAMa,oBAAoB,GAAGvD,QAAQ,CAACK,aAAa,CAACqC,gBAAgB;EAEpE,IAAIY,uBAAuB,KAAKC,oBAAoB,EAAE;IACpDvD,QAAQ,CAACE,IAAI,IAAIqD,oBAAoB,GAAGD,uBAAuB;EACjE;EAEA5G,SAAS,CAAC6D,GAAG,CAAC+B,KAAK,EAAEtC,QAAQ,CAAC;EAE9BvD,kBAAkB,CAAC6F,KAAK,EAAEtC,QAAQ,CAAC;EAEnCQ,mBAAmB,CAAC8B,KAAK,EAAE7B,eAAe,EAAE,KAAK,EAAEE,MAAM,CAAC;AAC5D", "ignoreList": []}