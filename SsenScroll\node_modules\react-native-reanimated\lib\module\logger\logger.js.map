{"version": 3, "names": ["addLogBoxLog", "DOCS_URL", "DOCS_REFERENCE", "LogLevel", "logToConsole", "data", "level", "console", "warn", "message", "content", "error", "DEFAULT_LOGGER_CONFIG", "logFunction", "strict", "formatMessage", "createLog", "formattedMessage", "substitutions", "category", "componentStack", "componentStackType", "stack", "Error", "logToLogBoxAndConsole", "registerLoggerConfig", "config", "global", "__reanimatedLoggerConfig", "replaceLoggerImplementation", "updateLoggerConfig", "options", "handleLog", "logger"], "sourceRoot": "../../../src", "sources": ["logger/logger.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,YAAY,QAAQ,aAAU;AAEvC,MAAMC,QAAQ,GACZ,wFAAwF;AAC1F,MAAMC,cAAc,GAAG,0FAA0FD,QAAQ,oBAAoB;AAI7I,WAAYE,QAAQ,0BAARA,QAAQ;EAARA,QAAQ,CAARA,QAAQ;EAARA,QAAQ,CAARA,QAAQ;EAAA,OAARA,QAAQ;AAAA;AAcpB,SAASC,YAAYA,CAACC,IAAa,EAAE;EACnC,SAAS;;EACT,QAAQA,IAAI,CAACC,KAAK;IAChB,KAAK,MAAM;MACTC,OAAO,CAACC,IAAI,CAACH,IAAI,CAACI,OAAO,CAACC,OAAO,CAAC;MAClC;IACF,KAAK,OAAO;IACZ,KAAK,OAAO;IACZ,KAAK,QAAQ;MACXH,OAAO,CAACI,KAAK,CAACN,IAAI,CAACI,OAAO,CAACC,OAAO,CAAC;MACnC;EACJ;AACF;AAEA,OAAO,MAAME,qBAA2C,GAAG;EACzDC,WAAW,EAAET,YAAY;EACzBE,KAAK,EAAEH,QAAQ,CAACK,IAAI;EACpBM,MAAM,EAAE;AACV,CAAC;AAED,SAASC,aAAaA,CAACN,OAAe,EAAE;EACtC,SAAS;;EACT,OAAO,gBAAgBA,OAAO,EAAE;AAClC;AAEA,SAASO,SAASA,CAACV,KAAqB,EAAEG,OAAe,EAAW;EAClE,SAAS;;EACT,MAAMQ,gBAAgB,GAAGF,aAAa,CAACN,OAAO,CAAC;EAE/C,OAAO;IACLH,KAAK;IACLG,OAAO,EAAE;MACPC,OAAO,EAAEO,gBAAgB;MACzBC,aAAa,EAAE;IACjB,CAAC;IACDC,QAAQ,EAAEF,gBAAgB;IAC1BG,cAAc,EAAE,EAAE;IAClBC,kBAAkB,EAAE,IAAI;IACxB;IACAC,KAAK,EAAE,IAAIC,KAAK,CAAC,CAAC,CAACD;EACrB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,qBAAqBA,CAACnB,IAAa,EAAE;EACnDL,YAAY,CAACK,IAAI,CAAC;EAClBD,YAAY,CAACC,IAAI,CAAC;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASoB,oBAAoBA,CAACC,MAA4B,EAAE;EACjE,SAAS;;EACTC,MAAM,CAACC,wBAAwB,GAAGF,MAAM;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,2BAA2BA,CAAChB,WAAwB,EAAE;EACpE,SAAS;;EACTY,oBAAoB,CAAC;IAAE,GAAGE,MAAM,CAACC,wBAAwB;IAAEf;EAAY,CAAC,CAAC;AAC3E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASiB,kBAAkBA,CAACC,OAA+B,EAAE;EAClE,SAAS;;EACTN,oBAAoB,CAAC;IACnB,GAAGE,MAAM,CAACC,wBAAwB;IAClC;IACAtB,KAAK,EAAEyB,OAAO,EAAEzB,KAAK,IAAIM,qBAAqB,CAACN,KAAK;IACpDQ,MAAM,EAAEiB,OAAO,EAAEjB,MAAM,IAAIF,qBAAqB,CAACE;EACnD,CAAC,CAAC;AACJ;AAMA,SAASkB,SAASA,CAChB1B,KAAkD,EAClDG,OAAe,EACfsB,OAAmB,EACnB;EACA,SAAS;;EACT,MAAML,MAAM,GAAGC,MAAM,CAACC,wBAAwB;EAC9C;EACE;EACA;EACCG,OAAO,CAACjB,MAAM,IAAI,CAACY,MAAM,CAACZ,MAAM;EACjC;EACAX,QAAQ,CAACG,KAAK,CAAC,GAAGoB,MAAM,CAACpB,KAAK,EAC9B;IACA;EACF;EAEA,IAAIyB,OAAO,CAACjB,MAAM,EAAE;IAClBL,OAAO,IAAI,OAAOP,cAAc,EAAE;EACpC;EAEAwB,MAAM,CAACb,WAAW,CAACG,SAAS,CAACV,KAAK,EAAEG,OAAO,CAAC,CAAC;AAC/C;AAEA,OAAO,MAAMwB,MAAM,GAAG;EACpBzB,IAAIA,CAACC,OAAe,EAAEsB,OAAmB,GAAG,CAAC,CAAC,EAAE;IAC9C,SAAS;;IACTC,SAAS,CAAC,MAAM,EAAEvB,OAAO,EAAEsB,OAAO,CAAC;EACrC,CAAC;EACDpB,KAAKA,CAACF,OAAe,EAAEsB,OAAmB,GAAG,CAAC,CAAC,EAAE;IAC/C,SAAS;;IACTC,SAAS,CAAC,OAAO,EAAEvB,OAAO,EAAEsB,OAAO,CAAC;EACtC;AACF,CAAC", "ignoreList": []}