{"version": 3, "names": ["measure", "getRelativeCoords", "animatedRef", "absoluteX", "absoluteY", "parentCoords", "x", "pageX", "y", "pageY"], "sourceRoot": "../../../src", "sources": ["platformFunctions/getRelativeCoords.ts"], "mappings": "AAAA,YAAY;;AAIZ,SAASA,OAAO,QAAQ,WAAW;;AAEnC;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAC/BC,WAAmC,EACnCC,SAAiB,EACjBC,SAAiB,EACO;EACxB,SAAS;;EACT,MAAMC,YAAY,GAAGL,OAAO,CAACE,WAAW,CAAC;EACzC,IAAIG,YAAY,KAAK,IAAI,EAAE;IACzB,OAAO,IAAI;EACb;EACA,OAAO;IACLC,CAAC,EAAEH,SAAS,GAAGE,YAAY,CAACE,KAAK;IACjCC,CAAC,EAAEJ,SAAS,GAAGC,YAAY,CAACI;EAC9B,CAAC;AACH", "ignoreList": []}