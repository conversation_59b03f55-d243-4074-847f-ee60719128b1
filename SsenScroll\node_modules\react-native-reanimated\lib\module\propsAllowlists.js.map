{"version": 3, "names": ["PropsAllowlists", "UI_THREAD_PROPS_WHITELIST", "opacity", "transform", "backgroundColor", "borderRightColor", "borderBottomColor", "borderColor", "borderEndColor", "borderLeftColor", "borderStartColor", "borderTopColor", "shadowOpacity", "shadowRadius", "scaleX", "scaleY", "translateX", "translateY", "NATIVE_THREAD_PROPS_WHITELIST", "borderBottomWidth", "borderEndWidth", "borderLeftWidth", "borderRightWidth", "borderStartWidth", "borderTopWidth", "borderWidth", "bottom", "boxShadow", "flex", "flexGrow", "flexShrink", "height", "left", "margin", "marginBottom", "marginEnd", "marginHorizontal", "marginLeft", "marginRight", "marginStart", "marginTop", "marginVertical", "maxHeight", "max<PERSON><PERSON><PERSON>", "minHeight", "min<PERSON><PERSON><PERSON>", "padding", "paddingBottom", "paddingEnd", "paddingHorizontal", "paddingLeft", "paddingRight", "paddingStart", "paddingTop", "paddingVertical", "right", "start", "top", "width", "zIndex", "borderBottomEndRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "borderBottomStartRadius", "borderRadius", "borderTopEndRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderTopStartRadius", "elevation", "fontSize", "lineHeight", "textShadowRadius", "textShadowOffset", "letterSpacing", "aspectRatio", "columnGap", "end", "flexBasis", "gap", "rowGap", "display", "backfaceVisibility", "overflow", "resizeMode", "fontStyle", "fontWeight", "textAlign", "textDecorationLine", "fontFamily", "textAlignVertical", "fontVariant", "textDecorationStyle", "textTransform", "writingDirection", "align<PERSON><PERSON><PERSON>", "alignItems", "alignSelf", "direction", "flexDirection", "flexWrap", "justifyContent", "position", "color", "tintColor", "shadowColor", "placeholderTextColor"], "sourceRoot": "../../src", "sources": ["propsAllowlists.ts"], "mappings": "AAAA,YAAY;;AAMZ,OAAO,MAAMA,eAAiC,GAAG;EAC/C;EACAC,yBAAyB,EAAE;IACzBC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACf;IACAC,eAAe,EAAE,IAAI;IACrBC,gBAAgB,EAAE,IAAI;IACtBC,iBAAiB,EAAE,IAAI;IACvBC,WAAW,EAAE,IAAI;IACjBC,cAAc,EAAE,IAAI;IACpBC,eAAe,EAAE,IAAI;IACrBC,gBAAgB,EAAE,IAAI;IACtBC,cAAc,EAAE,IAAI;IACpB;IACAC,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE,IAAI;IAClB;IACAC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE;EACd,CAAC;EACD;AACF;AACA;AACA;EACEC,6BAA6B,EAAE;IAC7BC,iBAAiB,EAAE,IAAI;IACvBC,cAAc,EAAE,IAAI;IACpBC,eAAe,EAAE,IAAI;IACrBC,gBAAgB,EAAE,IAAI;IACtBC,gBAAgB,EAAE,IAAI;IACtBC,cAAc,EAAE,IAAI;IACpBC,WAAW,EAAE,IAAI;IACjBC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE,IAAI;IACfC,gBAAgB,EAAE,IAAI;IACtBC,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE,IAAI;IACfC,cAAc,EAAE,IAAI;IACpBC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,IAAI;IACbC,aAAa,EAAE,IAAI;IACnBC,UAAU,EAAE,IAAI;IAChBC,iBAAiB,EAAE,IAAI;IACvBC,WAAW,EAAE,IAAI;IACjBC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,IAAI;IAChBC,eAAe,EAAE,IAAI;IACrBC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,qBAAqB,EAAE,IAAI;IAC3BC,sBAAsB,EAAE,IAAI;IAC5BC,uBAAuB,EAAE,IAAI;IAC7BC,uBAAuB,EAAE,IAAI;IAC7BC,YAAY,EAAE,IAAI;IAClBC,kBAAkB,EAAE,IAAI;IACxBC,mBAAmB,EAAE,IAAI;IACzBC,oBAAoB,EAAE,IAAI;IAC1BC,oBAAoB,EAAE,IAAI;IAC1BC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,gBAAgB,EAAE,IAAI;IACtBC,gBAAgB,EAAE,IAAI;IACtBC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE,IAAI;IAAE;IACjBC,GAAG,EAAE,IAAI;IAAE;IACXC,SAAS,EAAE,IAAI;IAAE;IACjBC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,IAAI;IACZ;IACAC,OAAO,EAAE,IAAI;IACbC,kBAAkB,EAAE,IAAI;IACxBC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,IAAI;IACfC,kBAAkB,EAAE,IAAI;IACxBC,UAAU,EAAE,IAAI;IAChBC,iBAAiB,EAAE,IAAI;IACvBC,WAAW,EAAE,IAAI;IACjBC,mBAAmB,EAAE,IAAI;IACzBC,aAAa,EAAE,IAAI;IACnBC,gBAAgB,EAAE,IAAI;IACtBC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IAAE;IACjBC,aAAa,EAAE,IAAI;IACnBC,QAAQ,EAAE,IAAI;IACdC,cAAc,EAAE,IAAI;IACpBC,QAAQ,EAAE,IAAI;IACd;IACAC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,IAAI;IACjBC,oBAAoB,EAAE;EACxB;AACF,CAAC", "ignoreList": []}