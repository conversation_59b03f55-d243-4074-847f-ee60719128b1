{"version": 3, "names": ["RNScreensTurboModule", "applyStyle", "applyStyleForBelowTopScreen", "BASE_VELOCITY", "ADDITIONAL_VELOCITY_FACTOR_X", "ADDITIONAL_VELOCITY_FACTOR_Y", "ADDITIONAL_VELOCITY_FACTOR_XY", "computeEasingProgress", "startingTimestamp", "distance", "velocity", "Math", "abs", "elapsedTime", "_getAnimationTimestamp", "currentPosition", "progress", "easing", "x", "pow", "computeProgress", "screenTransitionConfig", "event", "isTransitionCanceled", "screenDimensions", "progressX", "translationX", "width", "progressY", "translationY", "height", "max<PERSON>rogress", "max", "maybeScheduleNextFrame", "step", "didScreenReachDestination", "stackTag", "updateTransition", "requestAnimationFrame", "onFinishAnimation", "getSwipeSimulator", "lockAxis", "startTimestamp", "startingPosition", "y", "direction", "sign", "finalPosition", "euclideanDistance", "sqrt", "screenDiagonal", "velocityVectorLength", "didScreenReachDestinationCheck", "restoreOriginalStyleForBelowTopScreen", "computeFrame", "finished"], "sourceRoot": "../../../src", "sources": ["screenTransition/swipeSimulator.ts"], "mappings": "AAAA,YAAY;;AAMZ,SAASA,oBAAoB,QAAQ,2BAAwB;AAC7D,SAASC,UAAU,EAAEC,2BAA2B,QAAQ,mBAAgB;AAExE,MAAMC,aAAa,GAAG,GAAG;AACzB,MAAMC,4BAA4B,GAAG,GAAG;AACxC,MAAMC,4BAA4B,GAAG,GAAG;AACxC,MAAMC,6BAA6B,GAAG,GAAG;AAEzC,SAASC,qBAAqBA,CAC5BC,iBAAyB,EACzBC,QAAgB,EAChBC,QAAgB,EAChB;EACA,SAAS;;EACT,IAAIC,IAAI,CAACC,GAAG,CAACH,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC1B,OAAO,CAAC;EACV;EACA,MAAMI,WAAW,GAAG,CAACC,sBAAsB,CAAC,CAAC,GAAGN,iBAAiB,IAAI,IAAI;EACzE,MAAMO,eAAe,GAAGL,QAAQ,GAAGG,WAAW;EAC9C,MAAMG,QAAQ,GAAGD,eAAe,GAAGN,QAAQ;EAC3C,OAAOO,QAAQ;AACjB;AAEA,SAASC,MAAMA,CAACC,CAAS,EAAU;EACjC,SAAS;;EACT;EACA,OAAO,CAAC,GAAGP,IAAI,CAACQ,GAAG,CAAC,CAAC,GAAGD,CAAC,EAAE,CAAC,CAAC;AAC/B;AAEA,SAASE,eAAeA,CACtBC,sBAA8C,EAC9CC,KAAoC,EACpCC,oBAA6B,EAC7B;EACA,SAAS;;EACT,MAAMC,gBAAgB,GAAGH,sBAAsB,CAACG,gBAAgB;EAChE,MAAMC,SAAS,GAAGd,IAAI,CAACC,GAAG,CAACU,KAAK,CAACI,YAAY,GAAGF,gBAAgB,CAACG,KAAK,CAAC;EACvE,MAAMC,SAAS,GAAGjB,IAAI,CAACC,GAAG,CAACU,KAAK,CAACO,YAAY,GAAGL,gBAAgB,CAACM,MAAM,CAAC;EACxE,MAAMC,WAAW,GAAGpB,IAAI,CAACqB,GAAG,CAACP,SAAS,EAAEG,SAAS,CAAC;EAClD,MAAMZ,QAAQ,GAAGO,oBAAoB,GAAGQ,WAAW,GAAG,CAAC,GAAGA,WAAW;EACrE,OAAOf,QAAQ;AACjB;AAEA,SAASiB,sBAAsBA,CAC7BC,IAAgB,EAChBC,yBAAkC,EAClCd,sBAA8C,EAC9CC,KAAoC,EACpCC,oBAA6B,EAC7B;EACA,SAAS;;EACT,IAAI,CAACY,yBAAyB,EAAE;IAC9B,MAAMC,QAAQ,GAAGf,sBAAsB,CAACe,QAAQ;IAChD,MAAMpB,QAAQ,GAAGI,eAAe,CAC9BC,sBAAsB,EACtBC,KAAK,EACLC,oBACF,CAAC;IACDvB,oBAAoB,CAACqC,gBAAgB,CAACD,QAAQ,EAAEpB,QAAQ,CAAC;IACzDsB,qBAAqB,CAACJ,IAAI,CAAC;EAC7B,CAAC,MAAM;IACLb,sBAAsB,CAACkB,iBAAiB,GAAG,CAAC;EAC9C;AACF;AAEA,OAAO,SAASC,iBAAiBA,CAC/BlB,KAAoC,EACpCD,sBAA8C,EAC9CoB,QAAmB,EACnB;EACA,SAAS;;EACT,MAAMjB,gBAAgB,GAAGH,sBAAsB,CAACG,gBAAgB;EAChE,MAAMkB,cAAc,GAAG5B,sBAAsB,CAAC,CAAC;EAC/C,MAAM;IAAES;EAAqB,CAAC,GAAGF,sBAAsB;EACvD,MAAMsB,gBAAgB,GAAG;IACvBzB,CAAC,EAAEI,KAAK,CAACI,YAAY;IACrBkB,CAAC,EAAEtB,KAAK,CAACO;EACX,CAAC;EACD,MAAMgB,SAAS,GAAG;IAChB3B,CAAC,EAAEP,IAAI,CAACmC,IAAI,CAACxB,KAAK,CAACI,YAAY,CAAC;IAChCkB,CAAC,EAAEjC,IAAI,CAACmC,IAAI,CAACxB,KAAK,CAACO,YAAY;EACjC,CAAC;EACD,MAAMkB,aAAa,GAAGxB,oBAAoB,GACtC;IAAEL,CAAC,EAAE,CAAC;IAAE0B,CAAC,EAAE;EAAE,CAAC,GACd;IACE1B,CAAC,EAAE2B,SAAS,CAAC3B,CAAC,GAAGM,gBAAgB,CAACG,KAAK;IACvCiB,CAAC,EAAEC,SAAS,CAACD,CAAC,GAAGpB,gBAAgB,CAACM;EACpC,CAAC;EACL,MAAMrB,QAAQ,GAAG;IACfS,CAAC,EAAEP,IAAI,CAACC,GAAG,CAACmC,aAAa,CAAC7B,CAAC,GAAGyB,gBAAgB,CAACzB,CAAC,CAAC;IACjD0B,CAAC,EAAEjC,IAAI,CAACC,GAAG,CAACmC,aAAa,CAACH,CAAC,GAAGD,gBAAgB,CAACC,CAAC;EAClD,CAAC;EACD,MAAMT,yBAAyB,GAAG;IAChCjB,CAAC,EAAE,KAAK;IACR0B,CAAC,EAAE;EACL,CAAC;EACD,MAAMlC,QAAQ,GAAG;IAAEQ,CAAC,EAAEf,aAAa;IAAEyC,CAAC,EAAEzC;EAAc,CAAC;EACvD,IAAIsC,QAAQ,KAAK,GAAG,EAAE;IACpB/B,QAAQ,CAACkC,CAAC,GAAG,CAAC;IACdlC,QAAQ,CAACQ,CAAC,IACPd,4BAA4B,GAAGK,QAAQ,CAACS,CAAC,GAAIM,gBAAgB,CAACG,KAAK;EACxE,CAAC,MAAM,IAAIc,QAAQ,KAAK,GAAG,EAAE;IAC3B/B,QAAQ,CAACQ,CAAC,GAAG,CAAC;IACdR,QAAQ,CAACkC,CAAC,IACPvC,4BAA4B,GAAGI,QAAQ,CAACmC,CAAC,GAAIpB,gBAAgB,CAACM,MAAM;EACzE,CAAC,MAAM;IACL,MAAMkB,iBAAiB,GAAGrC,IAAI,CAACsC,IAAI,CAACxC,QAAQ,CAACS,CAAC,IAAI,CAAC,GAAGT,QAAQ,CAACmC,CAAC,IAAI,CAAC,CAAC;IACtE,MAAMM,cAAc,GAAGvC,IAAI,CAACsC,IAAI,CAC9BzB,gBAAgB,CAACG,KAAK,IAAI,CAAC,GAAGH,gBAAgB,CAACM,MAAM,IAAI,CAC3D,CAAC;IACD,MAAMqB,oBAAoB,GACxBhD,aAAa,GACZG,6BAA6B,GAAG0C,iBAAiB,GAAIE,cAAc;IACtE,IAAIvC,IAAI,CAACC,GAAG,CAAC+B,gBAAgB,CAACzB,CAAC,CAAC,GAAGP,IAAI,CAACC,GAAG,CAAC+B,gBAAgB,CAACC,CAAC,CAAC,EAAE;MAC/DlC,QAAQ,CAACQ,CAAC,GAAGiC,oBAAoB;MACjCzC,QAAQ,CAACkC,CAAC,GACRO,oBAAoB,GACpBxC,IAAI,CAACC,GAAG,CAAC+B,gBAAgB,CAACC,CAAC,GAAGD,gBAAgB,CAACzB,CAAC,CAAC;IACrD,CAAC,MAAM;MACLR,QAAQ,CAACQ,CAAC,GACRiC,oBAAoB,GACpBxC,IAAI,CAACC,GAAG,CAAC+B,gBAAgB,CAACzB,CAAC,GAAGyB,gBAAgB,CAACC,CAAC,CAAC;MACnDlC,QAAQ,CAACkC,CAAC,GAAGO,oBAAoB;IACnC;EACF;EAEA,IAAI5B,oBAAoB,EAAE;IACxB,SAAS6B,8BAA8BA,CAAA,EAAG;MACxC,IAAIX,QAAQ,KAAK,GAAG,EAAE;QACpB,OAAON,yBAAyB,CAACjB,CAAC;MACpC,CAAC,MAAM,IAAIuB,QAAQ,KAAK,GAAG,EAAE;QAC3B,OAAON,yBAAyB,CAACS,CAAC;MACpC,CAAC,MAAM;QACL,OAAOT,yBAAyB,CAACjB,CAAC,IAAIiB,yBAAyB,CAACS,CAAC;MACnE;IACF;IAEA,SAASS,qCAAqCA,CAAA,EAAG;MAC/C/B,KAAK,CAACI,YAAY,GAAGmB,SAAS,CAAC3B,CAAC,GAAGM,gBAAgB,CAACG,KAAK;MACzDL,KAAK,CAACO,YAAY,GAAGgB,SAAS,CAACD,CAAC,GAAGpB,gBAAgB,CAACM,MAAM;MAC1D5B,2BAA2B,CAACmB,sBAAsB,EAAEC,KAAK,CAAC;IAC5D;IAEA,MAAMgC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMtC,QAAQ,GAAG;QACfE,CAAC,EAAEX,qBAAqB,CAACmC,cAAc,EAAEjC,QAAQ,CAACS,CAAC,EAAER,QAAQ,CAACQ,CAAC,CAAC;QAChE0B,CAAC,EAAErC,qBAAqB,CAACmC,cAAc,EAAEjC,QAAQ,CAACmC,CAAC,EAAElC,QAAQ,CAACkC,CAAC;MACjE,CAAC;MACDtB,KAAK,CAACI,YAAY,GAChBiB,gBAAgB,CAACzB,CAAC,GAAG2B,SAAS,CAAC3B,CAAC,GAAGT,QAAQ,CAACS,CAAC,GAAGD,MAAM,CAACD,QAAQ,CAACE,CAAC,CAAC;MACpEI,KAAK,CAACO,YAAY,GAChBc,gBAAgB,CAACC,CAAC,GAAGC,SAAS,CAACD,CAAC,GAAGnC,QAAQ,CAACmC,CAAC,GAAG3B,MAAM,CAACD,QAAQ,CAAC4B,CAAC,CAAC;MACpE,IAAIC,SAAS,CAAC3B,CAAC,GAAG,CAAC,EAAE;QACnB,IAAII,KAAK,CAACI,YAAY,IAAI,CAAC,EAAE;UAC3BS,yBAAyB,CAACjB,CAAC,GAAG,IAAI;UAClCI,KAAK,CAACI,YAAY,GAAG,CAAC;QACxB;MACF,CAAC,MAAM;QACL,IAAIJ,KAAK,CAACI,YAAY,IAAI,CAAC,EAAE;UAC3BS,yBAAyB,CAACjB,CAAC,GAAG,IAAI;UAClCI,KAAK,CAACI,YAAY,GAAG,CAAC;QACxB;MACF;MACA,IAAImB,SAAS,CAACD,CAAC,GAAG,CAAC,EAAE;QACnB,IAAItB,KAAK,CAACO,YAAY,IAAI,CAAC,EAAE;UAC3BM,yBAAyB,CAACS,CAAC,GAAG,IAAI;UAClCtB,KAAK,CAACO,YAAY,GAAG,CAAC;QACxB;MACF,CAAC,MAAM;QACL,IAAIP,KAAK,CAACO,YAAY,IAAI,CAAC,EAAE;UAC3BM,yBAAyB,CAACS,CAAC,GAAG,IAAI;UAClCtB,KAAK,CAACO,YAAY,GAAG,CAAC;QACxB;MACF;MACA5B,UAAU,CAACoB,sBAAsB,EAAEC,KAAK,CAAC;MACzC,MAAMiC,QAAQ,GAAGH,8BAA8B,CAAC,CAAC;MACjD,IAAIG,QAAQ,EAAE;QACZF,qCAAqC,CAAC,CAAC;MACzC;MACApB,sBAAsB,CACpBqB,YAAY,EACZC,QAAQ,EACRlC,sBAAsB,EACtBC,KAAK,EACLC,oBACF,CAAC;IACH,CAAC;IACD,OAAO+B,YAAY;EACrB,CAAC,MAAM;IACL,MAAMA,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMtC,QAAQ,GAAG;QACfE,CAAC,EAAEX,qBAAqB,CAACmC,cAAc,EAAEjC,QAAQ,CAACS,CAAC,EAAER,QAAQ,CAACQ,CAAC,CAAC;QAChE0B,CAAC,EAAErC,qBAAqB,CAACmC,cAAc,EAAEjC,QAAQ,CAACmC,CAAC,EAAElC,QAAQ,CAACkC,CAAC;MACjE,CAAC;MACDtB,KAAK,CAACI,YAAY,GAChBiB,gBAAgB,CAACzB,CAAC,GAAG2B,SAAS,CAAC3B,CAAC,GAAGT,QAAQ,CAACS,CAAC,GAAGD,MAAM,CAACD,QAAQ,CAACE,CAAC,CAAC;MACpEI,KAAK,CAACO,YAAY,GAChBc,gBAAgB,CAACC,CAAC,GAAGC,SAAS,CAACD,CAAC,GAAGnC,QAAQ,CAACmC,CAAC,GAAG3B,MAAM,CAACD,QAAQ,CAAC4B,CAAC,CAAC;MACpE,IAAIC,SAAS,CAAC3B,CAAC,GAAG,CAAC,EAAE;QACnB,IAAII,KAAK,CAACI,YAAY,IAAIF,gBAAgB,CAACG,KAAK,EAAE;UAChDQ,yBAAyB,CAACjB,CAAC,GAAG,IAAI;UAClCI,KAAK,CAACI,YAAY,GAAGF,gBAAgB,CAACG,KAAK;QAC7C;MACF,CAAC,MAAM;QACL,IAAIL,KAAK,CAACI,YAAY,IAAI,CAACF,gBAAgB,CAACG,KAAK,EAAE;UACjDQ,yBAAyB,CAACjB,CAAC,GAAG,IAAI;UAClCI,KAAK,CAACI,YAAY,GAAG,CAACF,gBAAgB,CAACG,KAAK;QAC9C;MACF;MACA,IAAIkB,SAAS,CAACD,CAAC,GAAG,CAAC,EAAE;QACnB,IAAItB,KAAK,CAACO,YAAY,IAAIL,gBAAgB,CAACM,MAAM,EAAE;UACjDK,yBAAyB,CAACS,CAAC,GAAG,IAAI;UAClCtB,KAAK,CAACO,YAAY,GAAGL,gBAAgB,CAACM,MAAM;QAC9C;MACF,CAAC,MAAM;QACL,IAAIR,KAAK,CAACO,YAAY,IAAI,CAACL,gBAAgB,CAACM,MAAM,EAAE;UAClDK,yBAAyB,CAACS,CAAC,GAAG,IAAI;UAClCtB,KAAK,CAACO,YAAY,GAAG,CAACL,gBAAgB,CAACM,MAAM;QAC/C;MACF;MACA7B,UAAU,CAACoB,sBAAsB,EAAEC,KAAK,CAAC;MACzCW,sBAAsB,CACpBqB,YAAY,EACZnB,yBAAyB,CAACjB,CAAC,IAAIiB,yBAAyB,CAACS,CAAC,EAC1DvB,sBAAsB,EACtBC,KAAK,EACLC,oBACF,CAAC;IACH,CAAC;IACD,OAAO+B,YAAY;EACrB;AACF", "ignoreList": []}