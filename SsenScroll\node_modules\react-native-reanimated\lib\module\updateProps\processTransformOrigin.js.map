{"version": 3, "names": ["ReanimatedError", "INDEX_X", "INDEX_Y", "INDEX_Z", "validateTransformOrigin", "transform<PERSON><PERSON>in", "length", "x", "y", "z", "endsWith", "processTransformOrigin", "transformOriginIn", "Array", "isArray", "transformOriginString", "regex", "transformOriginArray", "index", "matches", "exec", "nextIndex", "value", "valueLower", "toLowerCase", "horizontal", "numericValue", "parseFloat", "isNaN", "__DEV__"], "sourceRoot": "../../../src", "sources": ["updateProps/processTransformOrigin.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,QAAQ,cAAW;AAE3C,MAAMC,OAAO,GAAG,CAAC;AACjB,MAAMC,OAAO,GAAG,CAAC;AACjB,MAAMC,OAAO,GAAG,CAAC;;AAEjB;AACA,SAASC,uBAAuBA,CAACC,eAAuC,EAAE;EACxE,SAAS;;EACT,IAAIA,eAAe,CAACC,MAAM,KAAK,CAAC,EAAE;IAChC,MAAM,IAAIN,eAAe,CAAC,8CAA8C,CAAC;EAC3E;EACA,MAAM,CAACO,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGJ,eAAe;EACjC,IAAI,EAAE,OAAOE,CAAC,KAAK,QAAQ,IAAK,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACG,QAAQ,CAAC,GAAG,CAAE,CAAC,EAAE;IAC1E,MAAM,IAAIV,eAAe,CACvB,sFAAsFO,CAAC,GACzF,CAAC;EACH;EACA,IAAI,EAAE,OAAOC,CAAC,KAAK,QAAQ,IAAK,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACE,QAAQ,CAAC,GAAG,CAAE,CAAC,EAAE;IAC1E,MAAM,IAAIV,eAAe,CACvB,sFAAsFQ,CAAC,GACzF,CAAC;EACH;EACA,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;IACzB,MAAM,IAAIT,eAAe,CACvB,+DAA+DS,CAAC,GAClE,CAAC;EACH;AACF;AAEA,OAAO,SAASE,sBAAsBA,CACpCC,iBAA8D,EACtC;EACxB,SAAS;;EACT,IAAIP,eAAuC,GAAGQ,KAAK,CAACC,OAAO,CAACF,iBAAiB,CAAC,GAC1EA,iBAAiB,GACjB,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;EAErB,IAAI,OAAOA,iBAAiB,KAAK,QAAQ,EAAE;IACzC,MAAMG,qBAAqB,GAAGH,iBAAiB;IAC/C,MAAMI,KAAK,GAAG,gDAAgD;IAC9D,MAAMC,oBAA4C,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;IAEtE,IAAIC,KAAK,GAAGjB,OAAO;IACnB,IAAIkB,OAAO;IACX,OAAQA,OAAO,GAAGH,KAAK,CAACI,IAAI,CAACL,qBAAqB,CAAC,EAAG;MACpD,IAAIM,SAAS,GAAGH,KAAK,GAAG,CAAC;MAEzB,MAAMI,KAAK,GAAGH,OAAO,CAAC,CAAC,CAAC;MACxB,MAAMI,UAAU,GAAGD,KAAK,CAACE,WAAW,CAAC,CAAC;MAEtC,QAAQD,UAAU;QAChB,KAAK,MAAM;QACX,KAAK,OAAO;UAAE;YACZ,IAAIL,KAAK,KAAKjB,OAAO,EAAE;cACrB,MAAM,IAAID,eAAe,CACvB,oBAAoBsB,KAAK,kCAC3B,CAAC;YACH;YACAL,oBAAoB,CAAChB,OAAO,CAAC,GAAGsB,UAAU,KAAK,MAAM,GAAG,CAAC,GAAG,MAAM;YAClE;UACF;QACA,KAAK,KAAK;QACV,KAAK,QAAQ;UAAE;YACb,IAAIL,KAAK,KAAKf,OAAO,EAAE;cACrB,MAAM,IAAIH,eAAe,CACvB,oBAAoBsB,KAAK,kCAC3B,CAAC;YACH;YACAL,oBAAoB,CAACf,OAAO,CAAC,GAAGqB,UAAU,KAAK,KAAK,GAAG,CAAC,GAAG,MAAM;;YAEjE;YACA,IAAIL,KAAK,KAAKjB,OAAO,EAAE;cACrB,MAAMwB,UAAU,GAAGT,KAAK,CAACI,IAAI,CAACL,qBAAqB,CAAC;cACpD,IAAIU,UAAU,IAAI,IAAI,EAAE;gBACtB;cACF;cAEA,QAAQA,UAAU,GAAG,CAAC,CAAC,CAACD,WAAW,CAAC,CAAC;gBACnC,KAAK,MAAM;kBACTP,oBAAoB,CAAChB,OAAO,CAAC,GAAG,CAAC;kBACjC;gBACF,KAAK,OAAO;kBACVgB,oBAAoB,CAAChB,OAAO,CAAC,GAAG,MAAM;kBACtC;gBACF,KAAK,QAAQ;kBACXgB,oBAAoB,CAAChB,OAAO,CAAC,GAAG,KAAK;kBACrC;gBACF;kBACE,MAAM,IAAID,eAAe,CACvB,qCAAqCe,qBAAqB,EAC5D,CAAC;cACL;cACAM,SAAS,GAAGlB,OAAO;YACrB;YAEA;UACF;QACA,KAAK,QAAQ;UAAE;YACb,IAAIe,KAAK,KAAKf,OAAO,EAAE;cACrB,MAAM,IAAIH,eAAe,CACvB,0BAA0BsB,KAAK,gCACjC,CAAC;YACH;YACAL,oBAAoB,CAACC,KAAK,CAAC,GAAG,KAAK;YACnC;UACF;QACA;UAAS;YACP,IAAII,KAAK,CAACZ,QAAQ,CAAC,GAAG,CAAC,EAAE;cACvBO,oBAAoB,CAACC,KAAK,CAAC,GAAGI,KAAK;YACrC,CAAC,MAAM;cACL,MAAMI,YAAY,GAAGC,UAAU,CAACL,KAAK,CAAC;cACtC,IAAIM,KAAK,CAACF,YAAY,CAAC,EAAE;gBACvB,MAAM,IAAI1B,eAAe,CACvB,8CAA8CsB,KAAK,EACrD,CAAC;cACH;cACAL,oBAAoB,CAACC,KAAK,CAAC,GAAGQ,YAAY;YAC5C;YACA;UACF;MACF;MAEAR,KAAK,GAAGG,SAAS;IACnB;IAEAhB,eAAe,GAAGY,oBAAoB;EACxC;EAEA,IACE,OAAOL,iBAAiB,KAAK,QAAQ,IACrC,CAACC,KAAK,CAACC,OAAO,CAACF,iBAAiB,CAAC,EACjC;IACA,MAAM,IAAIZ,eAAe,CACvB,iCAAiC,OAAOY,iBAAiB,EAC3D,CAAC;EACH;EAEA,IAAIiB,OAAO,EAAE;IACXzB,uBAAuB,CAACC,eAAe,CAAC;EAC1C;EAEA,OAAOA,eAAe;AACxB", "ignoreList": []}