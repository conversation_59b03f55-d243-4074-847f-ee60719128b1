{"version": 3, "names": ["ReanimatedError", "createJSWorkletsModule", "JSWorklets", "makeShareableClone"], "sourceRoot": "../../../../src", "sources": ["worklets/WorkletsModule/JSWorklets.ts"], "mappings": "AAAA,YAAY;;AAGZ,SAASA,eAAe,QAAQ,iBAAc;AAE9C,OAAO,SAASC,sBAAsBA,CAAA,EAAoB;EACxD,OAAO,IAAIC,UAAU,CAAC,CAAC;AACzB;AAEA,MAAMA,UAAU,CAA4B;EAC1CC,kBAAkBA,CAAA,EAAuB;IACvC,MAAM,IAAIH,eAAe,CACvB,0DACF,CAAC;EACH;AACF", "ignoreList": []}