{"version": 3, "file": "Easing.d.ts", "sourceRoot": "", "sources": ["../../src/Easing.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,eAAe,CAAC;AAEpD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4CG;AAEH,kEAAkE;AAClE,MAAM,MAAM,QAAQ,GAAG,cAAc,CAAC;AAEtC,MAAM,MAAM,qBAAqB,GAAG;IAAE,OAAO,EAAE,MAAM,cAAc,CAAA;CAAE,CAAC;AAEtE,yEAAyE;AACzE,MAAM,MAAM,eAAe,GAAG,qBAAqB,CAAC;AACpD;;;;;GAKG;AACH,iBAAS,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAGjC;AAED;;;;;GAKG;AACH,iBAAS,IAAI,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAG/B;AAED;;;;;GAKG;AACH,iBAAS,IAAI,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAG/B;AAED;;;;;GAKG;AACH,iBAAS,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAGhC;AAED;;;;GAIG;AACH,iBAAS,IAAI,CAAC,CAAC,EAAE,MAAM,GAAG,cAAc,CAMvC;AAED;;;;GAIG;AACH,iBAAS,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAG9B;AAED;;;;GAIG;AACH,iBAAS,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAGjC;AAED;;;;GAIG;AACH,iBAAS,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAG9B;AAED;;;;;;;;GAQG;AACH,iBAAS,OAAO,CAAC,UAAU,SAAI,GAAG,cAAc,CAO/C;AAED;;;;;;;GAOG;AACH,iBAAS,IAAI,CAAC,CAAC,SAAU,GAAG,CAAC,CAAC,EAAE,MAAM,KAAK,MAAM,CAMhD;AAED;;;;GAIG;AACH,iBAAS,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAkBjC;AAED;;;;;;GAMG;AACH,iBAAS,MAAM,CACb,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,GACT,qBAAqB,CAQvB;AAED,iBAAS,QAAQ,CACf,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,GACT,CAAC,CAAC,EAAE,MAAM,KAAK,MAAM,CAGvB;AAED,wCAAwC;AACxC,iBAAS,GAAG,CAAC,MAAM,EAAE,cAAc,GAAG,cAAc,CAGnD;AAED,yCAAyC;AACzC,iBAAS,GAAG,CAAC,MAAM,EAAE,cAAc,GAAG,cAAc,CAMnD;AAED;;;GAGG;AACH,iBAAS,KAAK,CAAC,MAAM,EAAE,cAAc,GAAG,cAAc,CASrD;AAED;;;;;;GAMG;AACH,iBAAS,KAAK,CAAC,CAAC,SAAK,EAAE,eAAe,UAAO,GAAG,cAAc,CAU7D;AAsBD,eAAO,MAAM,gBAAgB,eAAuB,CAAC;AAWrD,eAAO,MAAM,MAAM;;;;;;;;;;;;;;;;;;CAAe,CAAC"}