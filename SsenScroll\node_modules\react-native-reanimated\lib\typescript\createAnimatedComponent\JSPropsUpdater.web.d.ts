/// <reference types="react" />
import type { AnimatedComponentProps, IAnimatedComponentInternal, InitialComponentProps } from './commonTypes';
export default class JSPropsUpdaterWeb {
    addOnJSPropsChangeListener(_animatedComponent: React.Component<AnimatedComponentProps<InitialComponentProps>> & IAnimatedComponentInternal): void;
    removeOnJSPropsChangeListener(_animatedComponent: React.Component<AnimatedComponentProps<InitialComponentProps>> & IAnimatedComponentInternal): void;
}
//# sourceMappingURL=JSPropsUpdater.web.d.ts.map