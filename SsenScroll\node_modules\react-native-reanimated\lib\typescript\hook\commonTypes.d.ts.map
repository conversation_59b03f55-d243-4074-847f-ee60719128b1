{"version": 3, "file": "commonTypes.d.ts", "sourceRoot": "", "sources": ["../../../src/hook/commonTypes.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,SAAS,EAAE,gBAAgB,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,KAAK,EACV,UAAU,EACV,iBAAiB,EACjB,oBAAoB,EACpB,SAAS,EACT,SAAS,EACV,MAAM,cAAc,CAAC;AAEtB,OAAO,KAAK,EACV,4BAA4B,EAC5B,aAAa,EACb,iBAAiB,EACjB,WAAW,EACX,eAAe,EAChB,MAAM,gBAAgB,CAAC;AACxB,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,wCAAwC,CAAC;AAC5E,OAAO,KAAK,EAAE,qBAAqB,EAAE,MAAM,mCAAmC,CAAC;AAC/E,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAEhE,MAAM,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;AAExD,MAAM,WAAW,UAAU;IACzB,GAAG,EAAE,MAAM,GAAG,qBAAqB,CAAC;IACpC,IAAI,EAAE,MAAM,CAAC;IACb,iBAAiB,EAAE,iBAAiB,CAAC;CACtC;AAED,MAAM,WAAW,WAAW,CAAC,CAAC,SAAS,SAAS;IAC9C,CAAC,SAAS,CAAC,EAAE,CAAC,GACV,MAAM,GACN,iBAAiB,GACjB,WAAW,CAAC;IAChB,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC;IAClB,MAAM,EAAE,MAAM,MAAM,CAAC;CACtB;AAGD,MAAM,MAAM,eAAe,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC;AAErD,oFAAoF;AACpF,MAAM,MAAM,eAAe,GAAG;IAC5B,IAAI,MAAM,GAAG,iBAAiB,GAAG,IAAI,CAAC;IACtC;;;;OAIG;IACH,QAAQ,EAAE,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;CACtC,CAAC;AAEF,KAAK,iBAAiB,GAAG;IACvB,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF;;;;GAIG;AACH,MAAM,MAAM,eAAe,CAAC,KAAK,SAAS,MAAM,IAAI,iBAAiB,GACnE,CAAC,KAAK,SAAS;IACb,WAAW,EAAE,MAAM,WAAW,SAAS,MAAM,CAAC;CAC/C,GACG,WAAW,GACX,KAAK,CAAC,CAAC;AAEb,MAAM,MAAM,YAAY,CAAC,KAAK,SAAS,MAAM,IAAI,KAAK,SAAS;IAC7D,WAAW,EAAE,MAAM,WAAW,SAAS,MAAM,CAAC;CAC/C,GACG,WAAW,GACX,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AAE7B,MAAM,MAAM,kBAAkB,CAAC,KAAK,SAAS,MAAM,IAAI;IACrD,WAAW,EAAE,KAAK,CAAC;CACpB,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG,SAAS,GAAG,UAAU,GAAG,SAAS,CAAC;AAE9D,MAAM,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;AAE1E,MAAM,MAAM,qBAAqB,GAAG,eAAe,CAAC,mBAAmB,CAAC,CAAC;AAEzE,MAAM,WAAW,oBAAoB,CAAC,KAAK,SAAS,MAAM;IACxD,kBAAkB,EAAE,CAClB,UAAU,EAAE,CAAC,KAAK,EAAE,eAAe,CAAC,KAAK,CAAC,KAAK,IAAI,EACnD,SAAS,EAAE,MAAM,EAAE,KAChB,IAAI,CAAC;IACV,iBAAiB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,iBAAiB,CAAC,EAAE,MAAM,KAAK,IAAI,CAAC;IACzE,oBAAoB,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;CACjD;AAED,MAAM,WAAW,mBAAmB,CAClC,KAAK,SAAS,YAAY,GAAG,aAAa,GAAG,YAAY;IAEzD,eAAe,EAAE,kBAAkB,CAAC;IACpC,OAAO,EAAE;QACP,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5B,OAAO,EAAE,MAAM,aAAa,CAAC,KAAK,CAAC,CAAC;KACrC,CAAC;CACH;AAED,MAAM,WAAW,uBAAuB,CACtC,KAAK,SAAS,YAAY,GAAG,aAAa,GAAG,YAAY,CACzD,SAAQ,mBAAmB,CAAC,KAAK,CAAC;IAClC,kBAAkB,EACd,gBAAgB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,GACtC,gBAAgB,CAAC,aAAa,CAAC,CAAC;CACrC;AAED,MAAM,MAAM,wBAAwB,CAAC,KAAK,SAAS,YAAY,IAAI,CACjE,OAAO,EAAE,eAAe,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EACnD,YAAY,CAAC,EAAE,cAAc,GAAG,IAAI,EACpC,QAAQ,CAAC,EACL,4BAA4B,GAC5B,4BAA4B,EAAE,GAC9B,IAAI,EACR,eAAe,CAAC,EAAE,OAAO,KACtB,mBAAmB,CAAC,KAAK,CAAC,GAAG,uBAAuB,CAAC,KAAK,CAAC,CAAC"}