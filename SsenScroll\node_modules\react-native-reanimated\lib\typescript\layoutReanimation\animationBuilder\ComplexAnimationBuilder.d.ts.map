{"version": 3, "file": "ComplexAnimationBuilder.d.ts", "sourceRoot": "", "sources": ["../../../../src/layoutReanimation/animationBuilder/ComplexAnimationBuilder.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EACV,iBAAiB,EAEjB,cAAc,EACd,wBAAwB,EACxB,UAAU,EACX,MAAM,mBAAmB,CAAC;AAC3B,OAAO,KAAK,EAAE,qBAAqB,EAAE,MAAM,cAAc,CAAC;AAC1D,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAE9D,qBAAa,uBAAwB,SAAQ,oBAAoB;IAC/D,OAAO,CAAC,EAAE,cAAc,GAAG,qBAAqB,CAAC;IACjD,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,IAAI,CAAC,EAAE,iBAAiB,CAAC;IACzB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,0BAA0B,CAAC,EAAE,MAAM,CAAC;IACpC,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,aAAa,CAAC,EAAE,UAAU,CAAC;IAE3B,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,SAAS,OAAO,oBAAoB,EAC3D,IAAI,EAAE,CAAC,KACJ,YAAY,CAAC,CAAC,CAAC,CAAC;IAErB;;;;;;;OAOG;IACH,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,OAAO,uBAAuB,EACpD,IAAI,EAAE,CAAC,EACP,cAAc,EAAE,cAAc,GAAG,qBAAqB;IAMxD,MAAM,CAAC,cAAc,EAAE,cAAc,GAAG,qBAAqB,GAAG,IAAI;IAQpE;;;;;;OAMG;IACH,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,OAAO,uBAAuB,EACpD,IAAI,EAAE,CAAC,EACP,MAAM,EAAE,MAAM;IAMhB,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAK5B;;;;;;;OAOG;IACH,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,OAAO,uBAAuB,EACvD,IAAI,EAAE,CAAC,EACP,QAAQ,CAAC,EAAE,MAAM,GAChB,uBAAuB;IAK1B,SAAS,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI;IAMlC;;;;;;OAMG;IACH,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,OAAO,uBAAuB,EAC1D,IAAI,EAAE,CAAC,EACP,YAAY,EAAE,MAAM;IAMtB,YAAY,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAKjC;;;;;;;OAOG;IACH,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,OAAO,uBAAuB,EACrD,IAAI,EAAE,CAAC,EACP,OAAO,EAAE,MAAM;IAMjB,OAAO,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAK9B;;;;;;;OAOG;IACH,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,OAAO,uBAAuB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM;IAK3E,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAKxB;;;;;;OAMG;IACH,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,OAAO,uBAAuB,EACvD,IAAI,EAAE,CAAC,EACP,SAAS,EAAE,MAAM;IAMnB,SAAS,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI;IAKlC;;;;;;;OAOG;IACH,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,OAAO,uBAAuB,EAC/D,IAAI,EAAE,CAAC,EACP,iBAAiB,EAAE,MAAM;IAM3B,iBAAiB,CAAC,iBAAiB,EAAE,MAAM,GAAG,IAAI;IAKlD;;;;;;;OAOG;IACH,MAAM,CAAC,yBAAyB,CAAC,CAAC,SAAS,OAAO,uBAAuB,EACvE,IAAI,EAAE,CAAC,EACP,yBAAyB,EAAE,MAAM;IAMnC,yBAAyB,CAAC,yBAAyB,EAAE,MAAM;IAK3D;;;;;;;;OAQG;IACH,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,OAAO,uBAAuB,EAChE,IAAI,EAAE,CAAC,EACP,kBAAkB,EAAE,MAAM;IAM5B,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,GAAG,IAAI;IAKpD;;;;OAIG;IACH,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,OAAO,uBAAuB,EAC/D,IAAI,EAAE,CAAC,EACP,MAAM,EAAE,UAAU;IAMpB,iBAAiB,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI;IAK3C,qBAAqB,IAAI,wBAAwB;CAmDlD"}