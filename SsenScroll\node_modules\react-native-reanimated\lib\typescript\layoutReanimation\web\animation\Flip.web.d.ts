export declare const FlipInData: {
    FlipInYRight: {
        name: string;
        style: {
            0: {
                transform: {
                    perspective: string;
                    rotateY: string;
                    translateX: string;
                }[];
            };
            100: {
                transform: {
                    perspective: string;
                    rotateY: string;
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    FlipInYLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    perspective: string;
                    rotateY: string;
                    translateX: string;
                }[];
            };
            100: {
                transform: {
                    perspective: string;
                    rotateY: string;
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    FlipInXUp: {
        name: string;
        style: {
            0: {
                transform: {
                    perspective: string;
                    rotateX: string;
                    translateY: string;
                }[];
            };
            100: {
                transform: {
                    perspective: string;
                    rotateX: string;
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
    FlipInXDown: {
        name: string;
        style: {
            0: {
                transform: {
                    perspective: string;
                    rotateX: string;
                    translateY: string;
                }[];
            };
            100: {
                transform: {
                    perspective: string;
                    rotateX: string;
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
    FlipInEasyX: {
        name: string;
        style: {
            0: {
                transform: {
                    perspective: string;
                    rotateX: string;
                }[];
            };
            100: {
                transform: {
                    perspective: string;
                    rotateX: string;
                }[];
            };
        };
        duration: number;
    };
    FlipInEasyY: {
        name: string;
        style: {
            0: {
                transform: {
                    perspective: string;
                    rotateY: string;
                }[];
            };
            100: {
                transform: {
                    perspective: string;
                    rotateY: string;
                }[];
            };
        };
        duration: number;
    };
};
export declare const FlipOutData: {
    FlipOutYRight: {
        name: string;
        style: {
            0: {
                transform: {
                    perspective: string;
                    rotateY: string;
                    translateX: string;
                }[];
            };
            100: {
                transform: {
                    perspective: string;
                    rotateY: string;
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    FlipOutYLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    perspective: string;
                    rotateY: string;
                    translateX: string;
                }[];
            };
            100: {
                transform: {
                    perspective: string;
                    rotateY: string;
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    FlipOutXUp: {
        name: string;
        style: {
            0: {
                transform: {
                    perspective: string;
                    rotateX: string;
                    translateY: string;
                }[];
            };
            100: {
                transform: {
                    perspective: string;
                    rotateX: string;
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
    FlipOutXDown: {
        name: string;
        style: {
            0: {
                transform: {
                    perspective: string;
                    rotateX: string;
                    translateY: string;
                }[];
            };
            100: {
                transform: {
                    perspective: string;
                    rotateX: string;
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
    FlipOutEasyX: {
        name: string;
        style: {
            0: {
                transform: {
                    perspective: string;
                    rotateX: string;
                }[];
            };
            100: {
                transform: {
                    perspective: string;
                    rotateX: string;
                }[];
            };
        };
        duration: number;
    };
    FlipOutEasyY: {
        name: string;
        style: {
            0: {
                transform: {
                    perspective: string;
                    rotateY: string;
                }[];
            };
            100: {
                transform: {
                    perspective: string;
                    rotateY: string;
                }[];
            };
        };
        duration: number;
    };
};
export declare const FlipIn: {
    FlipInYRight: {
        style: string;
        duration: number;
    };
    FlipInYLeft: {
        style: string;
        duration: number;
    };
    FlipInXUp: {
        style: string;
        duration: number;
    };
    FlipInXDown: {
        style: string;
        duration: number;
    };
    FlipInEasyX: {
        style: string;
        duration: number;
    };
    FlipInEasyY: {
        style: string;
        duration: number;
    };
};
export declare const FlipOut: {
    FlipOutYRight: {
        style: string;
        duration: number;
    };
    FlipOutYLeft: {
        style: string;
        duration: number;
    };
    FlipOutXUp: {
        style: string;
        duration: number;
    };
    FlipOutXDown: {
        style: string;
        duration: number;
    };
    FlipOutEasyX: {
        style: string;
        duration: number;
    };
    FlipOutEasyY: {
        style: string;
        duration: number;
    };
};
//# sourceMappingURL=Flip.web.d.ts.map