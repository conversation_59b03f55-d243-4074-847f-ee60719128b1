{"name": "react-native-reanimated", "version": "3.17.5", "description": "More powerful alternative to Animated library for React Native.", "scripts": {"test": "jest", "lint": "yarn lint:js && yarn lint:plugin && yarn lint:common && yarn lint:android && yarn lint:apple", "lint:js": "eslint src __tests__ __typetests__ && yarn prettier --check src __tests__ __typetests__", "lint:plugin": "cd plugin && yarn lint", "lint:android": "./scripts/validate-android.sh && ./android/gradlew -p android spotlessCheck -q && ./scripts/cpplint.sh android/src && yarn format:android:cpp --dry-run -Werror && yarn lint:cmake", "lint:common": "./scripts/validate-common.sh && ./scripts/cpplint.sh Common && yarn format:common --dry-run -Werror", "lint:apple": "./scripts/validate-apple.sh && yarn format:apple --dry-run -Werror", "lint:cmake": "find ./android -type d \\( -name build -o -name .cxx \\) -prune -o -type f -name 'CMakeLists.txt' -print | xargs ./scripts/lint-cmake.sh", "format": "yarn format:js && yarn format:plugin && yarn format:apple && yarn format:android:java && yarn format:android:cpp && yarn format:android:cmake && yarn format:common", "format:js": "prettier --write --list-different src __tests__ __typetests__", "format:plugin": "cd plugin && yarn format", "format:apple": "find apple -iname \"*.h\" -o -iname \"*.m\" -o -iname \"*.mm\" -o -iname \"*.cpp\" | xargs clang-format -i", "format:android:java": "node ./scripts/format-java.js", "format:android:cpp": "find android/src -iname \"*.h\" -o -iname \"*.cpp\" | xargs clang-format -i", "format:android:cmake": "find ./android -type d \\( -name build -o -name .cxx \\) -prune -o -type f -name 'CMakeLists.txt' -print | xargs ./scripts/format-cmake.sh", "format:common": "find Common -iname \"*.h\" -o -iname \"*.cpp\" | xargs clang-format -i", "find-unused-code:js": "yarn ts-prune --ignore \"index|.web.\" --error", "type:check": "yarn type:check:src && yarn type:check:plugin && ./scripts/test-ts.sh ../../apps/common-app/src/App.tsx __typetests__/common", "type:check:src": "yarn tsc --noEmit", "type:check:plugin": "cd plugin && yarn type:check:src", "type:check:app": "./scripts/test-ts.sh ../../apps/common-app/src/App.tsx", "type:check:tests:common": "./scripts/test-ts.sh __typetests__/common", "build": "yarn build:plugin && bob build", "build:plugin": "cd plugin && yarn build", "circular-dependency-check": "yarn madge --extensions js,ts,tsx --circular src lib", "use-strict-check": "node ./scripts/validate-use-strict.js", "prepack": "cp ../../README.md ./README.md", "postpack": "rm ./README.md"}, "main": "lib/module/index", "module": "lib/module/index", "react-native": "src/index", "source": "src/index", "types": "lib/typescript/index.d.ts", "files": ["Common/", "src/", "lib/", "android/src/main/AndroidManifest.xml", "android/src/main/java/", "android/build.gradle", "android/", "apple/", "RNReanimated.podspec", "scripts/reanimated_utils.rb", "mock.js", "plugin/index.js", "metro-config", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__", "!apple/build/", "!android/build/", "!android/.cxx/", "!android/.gradle/", "!__snapshots__", "!*.test.js", "!*.test.js.map", "!**/node_modules"], "repository": {"type": "git", "url": "git+https://github.com/software-mansion/react-native-reanimated.git", "directory": "packages/react-native-reanimated"}, "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/software-mansion/react-native-reanimated/issues"}, "homepage": "https://docs.swmansion.com/react-native-reanimated", "dependencies": {"@babel/plugin-transform-arrow-functions": "^7.0.0-0", "@babel/plugin-transform-class-properties": "^7.0.0-0", "@babel/plugin-transform-classes": "^7.0.0-0", "@babel/plugin-transform-nullish-coalescing-operator": "^7.0.0-0", "@babel/plugin-transform-optional-chaining": "^7.0.0-0", "@babel/plugin-transform-shorthand-properties": "^7.0.0-0", "@babel/plugin-transform-template-literals": "^7.0.0-0", "@babel/plugin-transform-unicode-regex": "^7.0.0-0", "@babel/preset-typescript": "^7.16.7", "convert-source-map": "^2.0.0", "invariant": "^2.2.4", "react-native-is-edge-to-edge": "1.1.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0", "react": "*", "react-native": "*"}, "devDependencies": {"@babel/cli": "^7.20.0", "@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/types": "^7.20.0", "@react-native/babel-preset": "0.79.0-rc.4", "@react-native/eslint-config": "0.79.0-rc.4", "@react-native/metro-config": "0.79.0-rc.4", "@react-native/typescript-config": "0.79.0-rc.4", "@testing-library/jest-native": "^4.0.4", "@testing-library/react-hooks": "^8.0.0", "@testing-library/react-native": "^13.0.1", "@types/babel__core": "^7.20.0", "@types/babel__generator": "^7.6.4", "@types/babel__traverse": "^7.14.2", "@types/convert-source-map": "^2.0.0", "@types/invariant": "^2.2.35", "@types/jest": "^29.5.13", "@types/node": "^18.0.0", "@types/react": "^19.0.10", "@types/react-test-renderer": "^19.0.0", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "@typescript-eslint/rule-tester": "^6.21.0", "axios": "^1.7.4", "babel-eslint": "^10.1.0", "babel-plugin-module-resolver": "^5.0.0", "clang-format": "^1.6.0", "code-tag": "^1.1.0", "cspell": "^8.8.0", "eslint": "^8.57.0", "eslint-config-prettier": "^8.3.0", "eslint-config-standard": "^17.1.0", "eslint-import-resolver-babel-module": "^5.3.1", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-n": "^16.4.0", "eslint-plugin-no-inline-styles": "^1.0.5", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-reanimated": "workspace:*", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-tsdoc": "^0.2.17", "jest": "^29.0.0", "madge": "^5.0.1", "prettier": "^3.3.3", "react": "19.0.0", "react-native": "0.79.0-rc.4", "react-native-builder-bob": "patch:react-native-builder-bob@npm%3A0.33.1#~/.yarn/patches/react-native-builder-bob-npm-0.33.1-383d9e23a5.patch", "react-native-gesture-handler": "2.25.0", "react-native-web": "0.19.13", "react-test-renderer": "19.0.0", "shelljs": "^0.8.5", "ts-prune": "^0.10.3", "typescript": "~5.3.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true, "jsxRuntime": "classic"}], "typescript"]}, "codegenConfig": {"name": "rnreanimated", "type": "modules", "jsSrcsDir": "./src/specs", "android": {"javaPackageName": "com.swmansion.reanimated"}}, "sideEffects": ["./src/layoutReanimation/animationsManager.ts", "./lib/module/layoutReanimation/animationsManager.js", "./src/core.ts", "./lib/module/core.js", "./src/initializers.ts", "./lib/module/initializers.js", "./src/index.ts", "./lib/module/index.js"]}