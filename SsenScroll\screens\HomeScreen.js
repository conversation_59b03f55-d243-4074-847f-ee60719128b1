import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  Alert,
  Dimensions,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
  interpolateColor,
} from 'react-native-reanimated';
import { comickAPI, getCoverImageUrl } from '../services/api';

const { width, height } = Dimensions.get('window');

// --- Reusable UI Components (Unchanged) ---

const CarouselItem = React.memo(({ item, onPress }) => (
  <TouchableOpacity activeOpacity={0.9} style={styles.carouselCard} onPress={onPress}>
    <Image
      source={{ uri: getCoverImageUrl(item.md_covers?.[0]) }}
      style={styles.carouselImage}
      placeholder="blurhash|L6A,h2t70000_2t7IVxu00Rj?bRj"
      transition={500}
    />
    <LinearGradient
      colors={['transparent', 'rgba(21, 21, 21, 0)', 'rgba(21, 21, 21, 0.9)', '#1F1D2B']}
      style={styles.carouselGradient}
    />
    <View style={styles.carouselInfo}>
      <Text style={styles.carouselCategory}>Featured</Text>
      <Text style={styles.carouselTitle} numberOfLines={2}>{item.title}</Text>
    </View>
  </TouchableOpacity>
));

const Dot = ({ active }) => {
    const animation = useSharedValue(active ? 1 : 0);
    useEffect(() => {
        animation.value = withSpring(active ? 1 : 0, { damping: 15 });
    }, [active]);
    const animatedStyle = useAnimatedStyle(() => {
        const width = interpolate(animation.value, [0, 1], [6, 16]);
        const backgroundColor = interpolateColor(
            animation.value, [0, 1], ['rgba(255, 255, 255, 0.4)', '#FFF']
        );
        return { width, backgroundColor };
    });
    return <Animated.View style={[styles.dot, animatedStyle]} />;
};

const PaginationDots = ({ data, activeIndex }) => (
    <View style={styles.paginationContainer}>
        {data.map((_, i) => ( <Dot key={`dot-${i}`} active={i === activeIndex} /> ))}
    </View>
);

const SectionHeader = ({ title }) => (
    <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{title}</Text>
        <TouchableOpacity>
            <Text style={styles.seeAllText}>see all</Text>
        </TouchableOpacity>
    </View>
);

const PosterItem = React.memo(({ item, onPress, renderAs }) => {
  const comic = renderAs === 'chapter' ? item.md_comics : item;
  if (!comic) return null;

  return (
    <TouchableOpacity activeOpacity={0.8} style={styles.posterCard} onPress={() => onPress(comic)}>
        <Image
          source={{ uri: getCoverImageUrl(comic.md_covers?.[0]) }}
          style={styles.posterImage}
          placeholder="blurhash|L07[g_j[fQfQ00fQfQfQ00j[fQj["
        />
        <View style={styles.posterInfo}>
          <Text style={styles.posterTitle} numberOfLines={1}>{comic.title}</Text>
          {renderAs === 'chapter' && (
            <Text style={styles.posterSubtitle} numberOfLines={1}>
              Chapter {item.chap}
            </Text>
          )}
        </View>
    </TouchableOpacity>
  );
});

const PosterSection = ({ title, data, renderAs, onNavigate }) => (
    <View>
      <SectionHeader title={title} />
      <FlatList
        data={data}
        renderItem={({ item }) => <PosterItem item={item} onPress={onNavigate} renderAs={renderAs} />}
        keyExtractor={(item) => `${renderAs}-${item.slug || item.hid}`}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 15, paddingBottom: 10 }}
      />
    </View>
);

const LoadingScreen = () => (
  <View style={[styles.container, {justifyContent: 'center', alignItems: 'center'}]}>
    <ActivityIndicator size="large" color="#FFF" />
  </View>
);

// --- Main HomeScreen ---
// The `navigation` prop is automatically passed by React Navigation.
export default function HomeScreen({ navigation }) {
  const [featured, setFeatured] = useState([]);
  const [popular, setPopular] = useState([]);
  const [latestChapters, setLatestChapters] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);

  const carouselViewabilityConfig = { viewAreaCoveragePercentThreshold: 50 };
  const onCarouselViewableItemsChanged = useCallback(({ viewableItems }) => {
    if (viewableItems.length > 0) setActiveIndex(viewableItems[0].index || 0);
  }, []);

  const loadData = async () => {
    try {
      const [trendingData, latestData] = await Promise.all([
        comickAPI.getTrendingComics(),
        comickAPI.getLatestChapters({ limit: 15 })
      ]);

      let comicsWithFollowers = [];
      if (trendingData.trending) {
        Object.values(trendingData.trending).forEach(dayComics => {
          if (Array.isArray(dayComics)) {
            comicsWithFollowers = [...comicsWithFollowers, ...dayComics];
          }
        });
      }

      const fallbackComics = trendingData.rank || [];
      const allComics = comicsWithFollowers.length > 0 ? comicsWithFollowers : fallbackComics;

      setFeatured(allComics.slice(0, 3));
      setPopular(allComics.slice(3, 20));
      setLatestChapters(latestData || []);

    } catch (error) {
      Alert.alert('Error', 'Failed to load data. Please try again.');
    } finally {
      setLoading(false); setRefreshing(false);
    }
  };


  useEffect(() => { loadData() }, []);
  const onRefresh = useCallback(() => { setRefreshing(true); loadData() }, []);

  const navigateToDetail = (comic) => {
    if (!comic) return;
    navigation.navigate('ManhwaDetail', { slug: comic.slug, hid: comic.hid, title: comic.title });
  };

  if (loading && !refreshing) {
    return <LoadingScreen />;
  }

  const screenData = [
      { type: 'top_mangas', title: 'Top Mangas', data: popular, renderAs: 'comic' },
      { type: 'latest_chapters', title: 'Latest Chapters', data: latestChapters, renderAs: 'chapter' },
      
  ];

  return (
    <View style={styles.container}>
      <FlatList
        data={screenData}
        keyExtractor={(item) => item.type}
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#FFF" />}
        ListHeaderComponent={
          <>
            <View style={{height: height * 0.70}}>
                <FlatList
                  data={featured}
                  renderItem={({ item }) => <CarouselItem item={item} onPress={() => navigateToDetail(item)} />}
                  keyExtractor={(item) => `featured-${item.slug}`}
                  horizontal pagingEnabled showsHorizontalScrollIndicator={false}
                  onViewableItemsChanged={onCarouselViewableItemsChanged}
                  viewabilityConfig={carouselViewabilityConfig}
                />
            </View>
            <PaginationDots data={featured} activeIndex={activeIndex} />
          </>
        }
        renderItem={({item}) => (
          <PosterSection
            title={item.title}
            data={item.data}
            renderAs={item.renderAs}
            onNavigate={navigateToDetail}
          />
        )}
        contentContainerStyle={{ paddingBottom: 40 }}
      />
      <View style={styles.topBar}>
          <Text style={styles.logo}>M</Text>
          <View style={styles.topBarIcons}>
              {/* ADDED: onPress handler to navigate to the Search screen */}
              <TouchableOpacity
                style={styles.iconBg}
                onPress={() => navigation.navigate('Search')}
              >
                  <Ionicons name="search" size={20} color="#FFF"/>
              </TouchableOpacity>
              <TouchableOpacity style={styles.iconBg}>
                  <Ionicons name="settings-outline" size={20} color="#FFF"/>
              </TouchableOpacity>
          </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1F1D2B',
  },
  topBar: {
      position: 'absolute',
      top: 50,
      left: 0,
      right: 0,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      zIndex: 10,
  },
  logo: {
      fontSize: 32,
      fontWeight: 'bold',
      color: '#FFF',
  },
  topBarIcons: {
      flexDirection: 'row',
  },
  iconBg: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: 'rgba(50, 50, 50, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: 10,
  },
  carouselCard: {
      width: width,
      height: '100%',
      justifyContent: 'flex-end',
  },
  carouselImage: {
      ...StyleSheet.absoluteFillObject,
  },
  carouselGradient: {
      ...StyleSheet.absoluteFillObject,
  },
  carouselInfo: {
      padding: 20,
      paddingBottom: 60,
  },
  carouselCategory: {
      color: '#A39DCE',
      fontSize: 14,
      fontWeight: '600',
      marginBottom: 5,
  },
  carouselTitle: {
      color: '#FFF',
      fontSize: 28,
      fontWeight: 'bold',
  },
  paginationContainer: {
    flexDirection: 'row',
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    height: 16,
    position: 'absolute',
    top: height * 0.70 - 40,
    zIndex: 5,
  },
  dot: {
    height: 6,
    borderRadius: 3,
    marginHorizontal: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 30,
    paddingBottom: 20,
    paddingHorizontal: 15,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFF',
  },
  seeAllText: {
      color: '#A39DCE',
      fontSize: 14,
      fontWeight: '600',
  },
  posterCard: {
    width: 140,
    marginRight: 15,
  },
  posterImage: {
    width: 140,
    height: 210,
    borderRadius: 16,
    backgroundColor: '#333'
  },
  posterInfo: {
      marginTop: 10,
  },
  posterTitle: {
      color: '#FFF',
      fontSize: 15,
      fontWeight: '600',
      marginBottom: 2,
  },
  posterSubtitle: {
      color: '#A39DCE',
      fontSize: 12,
      fontWeight: '500',
  },
});