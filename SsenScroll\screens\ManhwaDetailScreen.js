import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  Dimensions,
  ActivityIndicator,
  StatusBar,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import Animated, { useSharedValue, useAnimatedStyle, withSpring } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { comickAPI, getCoverImageUrl, formatStatus } from '../services/api';

const { width } = Dimensions.get('window');
const TABS = ['Chapters', 'Details', 'Similar'];

// --- Helper Functions ---
const formatFollowCount = (count) => {
  if (!count) return '0';
  if (count < 1000) return count.toString();
  return `${(count / 1000).toFixed(1).replace('.0', '')}k`;
};

// --- Reusable UI Components ---

// FIX: TopNav is now a "dumb" component that receives insets as a prop.
const TopNav = ({ onBack, insets }) => (
  <View style={[styles.topNavContainer, { top: insets.top }]}>
    <View style={styles.topNav}>
      <TouchableOpacity style={styles.iconButton} onPress={onBack}>
        <Ionicons name="arrow-back" size={24} color="#FFF" />
      </TouchableOpacity>
      <View style={{ flexDirection: 'row' }}>
        <TouchableOpacity style={styles.iconButton}><Ionicons name="heart-outline" size={24} color="#FFF" /></TouchableOpacity>
        <TouchableOpacity style={styles.iconButton}><Ionicons name="share-social-outline" size={24} color="#FFF" /></TouchableOpacity>
      </View>
    </View>
  </View>
);

const ComicHeader = ({ comic, authorName, onContinue }) => (
  <View style={styles.headerContent}>
    <Image
      source={{ uri: getCoverImageUrl(comic?.md_covers?.[0]) }}
      style={styles.coverImage}
      placeholder="blurhash|L07[g_j[fQfQ00fQfQfQ00j[fQj["
    />
    <View style={styles.headerInfo}>
      <Text style={styles.title} numberOfLines={3}>{comic?.title}</Text>
      <Text style={styles.author}>By {authorName || 'Unknown'}</Text>
      <View style={styles.statsRow}>
        <Ionicons name="flag" size={14} color="#FFC107" /><Text style={styles.statText}>{formatStatus(comic?.status)}</Text>
      </View>
      <View style={styles.statsRow}>
        <Ionicons name="star" size={14} color="#FFC107" /><Text style={styles.statText}>{parseFloat(comic?.bayesian_rating || 0).toFixed(1)}</Text>
        <Ionicons name="heart" size={14} color="#FF6B6B" style={{ marginLeft: 15 }} /><Text style={styles.statText}>{formatFollowCount(comic?.user_follow_count)}</Text>
      </View>
      <TouchableOpacity style={styles.continueButton} onPress={onContinue}><Text style={styles.continueButtonText}>Continue</Text></TouchableOpacity>
    </View>
  </View>
);

const CustomTabBar = ({ activeTab, onTabPress, chapterCount }) => {
  const translateX = useSharedValue(0);
  useEffect(() => {
    const newIndex = TABS.indexOf(activeTab);
    translateX.value = withSpring(newIndex * (width / TABS.length), { damping: 15, stiffness: 120 });
  }, [activeTab]);
  const animatedIndicatorStyle = useAnimatedStyle(() => ({ transform: [{ translateX: translateX.value }] }));

  return (
    <View style={styles.tabBarContainer}>
      <View style={styles.tabBar}>
        {TABS.map((tab) => (
          <TouchableOpacity key={tab} style={styles.tab} onPress={() => onTabPress(tab)}>
            <Text style={[styles.tabText, activeTab === tab && styles.tabTextActive]}>
              {tab} {tab === 'Chapters' && `(${chapterCount})`}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      <Animated.View style={[styles.tabIndicator, animatedIndicatorStyle]} />
    </View>
  );
};

const DetailsContent = ({ comic }) => (
  <View style={styles.contentSection}>
    <Text style={styles.sectionTitle}>Description</Text>
    <Text style={styles.description}>{comic?.desc || "No description available."}</Text>
    <Text style={styles.sectionTitle}>Genres</Text>
    <View style={styles.genresContainer}>
      {comic?.md_comic_md_genres?.map((genreItem, index) => (
        <View key={index} style={styles.genreChip}><Text style={styles.genreText}>{genreItem.md_genres.name}</Text></View>
      ))}
    </View>
  </View>
);

const SimilarItem = ({ comic, onNavigate }) => (
    <TouchableOpacity style={styles.similarCard} onPress={() => onNavigate(comic)}>
        <Image 
            source={{uri: getCoverImageUrl(comic?.md_covers?.[0])}}
            style={styles.similarImage}
            placeholder="blurhash|L07[g_j[fQfQ00fQfQfQ00j[fQj["
        />
        <Text style={styles.similarTitle} numberOfLines={2}>{comic.title}</Text>
    </TouchableOpacity>
);

const SimilarContent = ({ recommendations, onNavigate }) => {
    const similarComics = recommendations?.map(rec => rec.relates) || [];
    if (similarComics.length === 0) {
        return <View style={styles.contentSection}><Text style={styles.description}>No similar comics found.</Text></View>
    }
    return (
        <View style={styles.contentSection}>
             <Text style={styles.sectionTitle}>Similar Comics</Text>
             <View style={styles.similarGrid}>
                {similarComics.map(comic => comic ? <SimilarItem key={comic.hid} comic={comic} onNavigate={onNavigate} /> : null)}
             </View>
        </View>
    );
};

const ChapterGroupItem = ({ group, onNavigate }) => {
    const [isExpanded, setExpanded] = useState(false);
    const hasMultiple = group.chapters.length > 1;

    return (
        <View style={styles.chapterGroupContainer}>
            <View style={styles.chapterItem}>
                <TouchableOpacity style={styles.chapterTitleButton} onPress={() => onNavigate(group.defaultChapter)}>
                    <View>
                        <Text style={styles.chapterTitle}>Chapter {group.chapterNumber}</Text>
                        <Text style={styles.chapterDate}>{new Date(group.defaultChapter.created_at).toLocaleDateString()}</Text>
                    </View>
                </TouchableOpacity>
                {hasMultiple && (
                    <TouchableOpacity style={styles.scanGroupToggle} onPress={() => setExpanded(!isExpanded)}>
                         <Ionicons name={isExpanded ? "close-circle-outline" : "ellipsis-vertical"} size={22} color="#8A8899" />
                    </TouchableOpacity>
                )}
            </View>
            {isExpanded && hasMultiple && (
                <View style={styles.expandedScansContainer}>
                    {group.chapters.map(chapter => (
                        <TouchableOpacity key={chapter.hid} style={styles.scanItem} onPress={() => onNavigate(chapter)}>
                            <Text style={styles.scanGroupName}>{chapter.group_name?.[0] || 'Unknown'}</Text>
                            <Text style={styles.scanDate}>{new Date(chapter.created_at).toLocaleDateString()}</Text>
                        </TouchableOpacity>
                    ))}
                </View>
            )}
        </View>
    )
}

const LoadingScreen = () => (<View style={styles.fullScreenLoader}><ActivityIndicator size="large" color="#FFF" /></View>);

// --- Main ManhwaDetailScreen Component ---
export default function ManhwaDetailScreen({ route, navigation }) {
  const { slug, hid } = route.params;
  const [comicDetails, setComicDetails] = useState(null);
  const [groupedChapters, setGroupedChapters] = useState([]);
  const [allChaptersRaw, setAllChaptersRaw] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('Chapters');
  
  // FIX: All hooks are now called at the top level, before any conditional returns.
  const insets = useSafeAreaInsets();

  const loadAllData = async () => {
      try {
        const comicData = await comickAPI.getComicDetails(slug);
        setComicDetails(comicData);
        
        const comicHid = hid || comicData.comic?.hid;
        if (comicHid) {
          const chaptersData = await comickAPI.getComicChapters(comicHid, { limit: 2000 });
          const englishChapters = (chaptersData.chapters || []).filter(c => c.lang === 'en');
          setAllChaptersRaw(englishChapters);
          const chapterGroups = englishChapters.reduce((acc, chapter) => {
              const chapNum = chapter.chap || 'Unknown';
              if (!acc[chapNum]) acc[chapNum] = [];
              acc[chapNum].push(chapter);
              return acc;
          }, {});
          const groupedChaptersArray = Object.entries(chapterGroups)
              .map(([chapNum, chapters]) => ({
                chapterNumber: chapNum,
                chapters: chapters.sort((a, b) => new Date(b.created_at) - new Date(a.created_at)),
                defaultChapter: chapters[0]
              }))
              .sort((a, b) => parseFloat(b.chapterNumber) - parseFloat(a.chapterNumber));
          setGroupedChapters(groupedChaptersArray);
        }
      } catch (error) { Alert.alert('Error', 'Failed to load details.'); }
      finally { setLoading(false); }
  };

  useEffect(() => { loadAllData() }, [slug]);

  const navigateToChapterReader = (chapter) => {
      navigation.navigate('ChapterReader', { currentChapter: chapter, allChapters: allChaptersRaw });
  }
  
  const navigateToSimilarDetail = (similarComic) => {
      navigation.push('ManhwaDetail', { slug: similarComic.slug, hid: similarComic.hid });
  }

  const handleContinue = () => {
    if(groupedChapters.length > 0 && groupedChapters[0].defaultChapter)
      navigateToChapterReader(groupedChapters[0].defaultChapter)
    else Alert.alert("No Chapters", "No chapters are available yet.")
  }
  
  const ListHeader = () => (
    <>
      {/* This paddingTop now respects the safe area AND provides space for the TopNav */}
      <View style={{ paddingTop: insets.top + 50 }} />
      <ComicHeader 
        comic={comicDetails?.comic} 
        authorName={comicDetails?.authors?.[0]?.name}
        onContinue={handleContinue} 
      />
      <CustomTabBar 
        activeTab={activeTab} 
        onTabPress={setActiveTab} 
        chapterCount={groupedChapters.length} 
      />
    </>
  );

  if (loading || !comicDetails) return <LoadingScreen />;

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <Image
        source={{ uri: getCoverImageUrl(comicDetails?.comic?.md_covers?.[1] || comicDetails?.comic?.md_covers?.[0]) }}
        style={styles.backgroundImage}
        blurRadius={25}
      />
      <View style={styles.backgroundOverlay} />

      <FlatList
        style={{ backgroundColor: 'transparent' }}
        data={activeTab === 'Chapters' ? groupedChapters : []}
        keyExtractor={item => (typeof item === 'string' ? item : item.chapterNumber)}
        ListHeaderComponent={ListHeader}
        showsVerticalScrollIndicator={false}
        renderItem={({ item }) => <ChapterGroupItem group={item} onNavigate={navigateToChapterReader} />}
        ListFooterComponent={
            <>
            {activeTab === 'Details' && <DetailsContent comic={comicDetails?.comic} />}
            {activeTab === 'Similar' && <SimilarContent recommendations={comicDetails?.comic?.recommendations} onNavigate={navigateToSimilarDetail} />}
            </>
        }
        contentContainerStyle={{ paddingBottom: 40 }}
      />

      {/* FIX: The `insets` object is now passed down as a prop */}
      <TopNav onBack={() => navigation.goBack()} insets={insets} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#1F1D2B' },
  fullScreenLoader: { flex: 1, backgroundColor: '#1F1D2B', justifyContent: 'center', alignItems: 'center' },
  backgroundImage: { ...StyleSheet.absoluteFillObject, opacity: 0.3 },
  backgroundOverlay: { ...StyleSheet.absoluteFillObject, backgroundColor: 'rgba(31, 29, 43, 0.85)' },
  topNavContainer: { position: 'absolute', top: 0, left: 0, right: 0, zIndex: 10 },
  topNav: { flexDirection: 'row', justifyContent: 'space-between', paddingHorizontal: 15, paddingVertical: 10 },
  iconButton: { width: 44, height: 44, borderRadius: 22, backgroundColor: 'rgba(0,0,0,0.2)', justifyContent: 'center', alignItems: 'center' },
  headerContent: { flexDirection: 'row', paddingHorizontal: 20, paddingTop: 10, paddingBottom: 20, alignItems: 'flex-end', backgroundColor: 'transparent' },
  coverImage: { width: 120, height: 180, borderRadius: 12 },
  headerInfo: { flex: 1, marginLeft: 20, height: 180, justifyContent: 'space-between' },
  title: { color: '#FFF', fontSize: 22, fontWeight: 'bold' },
  author: { color: '#A39DCE', fontSize: 14, marginTop: 4 },
  statsRow: { flexDirection: 'row', alignItems: 'center', marginTop: 8 },
  statText: { color: '#FFF', fontSize: 14, fontWeight: '600', marginLeft: 6 },
  continueButton: { backgroundColor: '#5E5CE6', borderRadius: 25, height: 44, justifyContent: 'center', alignItems: 'center', marginTop: 10 },
  continueButtonText: { color: '#FFF', fontSize: 16, fontWeight: 'bold' },
  tabBarContainer: { backgroundColor: '#1F1D2B', marginTop: 30 },
  tabBar: { flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: '#2D2A41' },
  tab: { flex: 1, alignItems: 'center', paddingVertical: 15 },
  tabText: { color: '#8A8899', fontSize: 16, fontWeight: '500' },
  tabTextActive: { color: '#FFF', fontWeight: 'bold' },
  tabIndicator: { height: 3, width: width / TABS.length, backgroundColor: '#5E5CE6', position: 'absolute', bottom: -1 },
  contentSection: { padding: 20, backgroundColor: '#1F1D2B', minHeight: 300 },
  sectionTitle: { color: '#FFF', fontSize: 20, fontWeight: 'bold', marginBottom: 15 },
  description: { color: '#D0CFD4', fontSize: 15, lineHeight: 22, marginBottom: 20 },
  genresContainer: { flexDirection: 'row', flexWrap: 'wrap' },
  genreChip: { backgroundColor: '#2D2A41', paddingHorizontal: 12, paddingVertical: 6, borderRadius: 15, marginRight: 10, marginBottom: 10 },
  genreText: { color: '#A39DCE', fontSize: 13, fontWeight: '500' },
  chapterGroupContainer: { borderBottomWidth: 1, borderBottomColor: '#2D2A41', paddingHorizontal: 20, backgroundColor: '#1F1D2B' },
  chapterItem: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 12 },
  chapterTitleButton: { flex: 1 },
  chapterTitle: { color: '#EAEAEA', fontSize: 16, fontWeight: '500' },
  chapterDate: { color: '#8A8899', fontSize: 13, marginTop: 4 },
  scanGroupToggle: { padding: 8 },
  expandedScansContainer: { paddingLeft: 15, paddingBottom: 5, backgroundColor: 'rgba(0,0,0,0.2)' },
  scanItem: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 10, borderTopWidth: 1, borderTopColor: 'rgba(45, 42, 65, 0.5)' },
  scanGroupName: { color: '#A39DCE', fontSize: 14 },
  scanDate: { color: '#8A8899', fontSize: 13 },
  similarGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' },
  similarCard: { width: (width - 60) / 2, marginBottom: 20 },
  similarImage: { width: '100%', height: ((width - 60) / 2) * 1.5, borderRadius: 12, backgroundColor: '#333' },
  similarTitle: { color: '#FFF', fontSize: 15, fontWeight: '600', marginTop: 8 },
});