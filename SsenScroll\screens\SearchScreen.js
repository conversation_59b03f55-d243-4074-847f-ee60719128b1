import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Animated, { useSharedValue, withTiming } from 'react-native-reanimated';
import { comickAPI, getCoverImageUrl } from '../services/api';

const SEARCH_HISTORY_KEY = '@search_history';

// --- Reusable UI Components for the New Search Screen ---

// Redesigned Search Bar with a back button
const SearchBar = ({ query, onQueryChange, onClear, onSubmit, onBack }) => (
    <View style={styles.searchBarRow}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
            <Ionicons name="arrow-back" size={24} color="#D0CFD4" />
        </TouchableOpacity>
        <View style={styles.searchBarContainer}>
            <Ionicons name="search" size={22} color="#8A8899" />
            <TextInput
                style={styles.searchInput}
                placeholder="Search"
                placeholderTextColor="#8A8899"
                value={query}
                onChangeText={onQueryChange}
                onSubmitEditing={onSubmit}
                returnKeyType="search"
                autoFocus={true} // For better UX, open keyboard immediately
            />
            {query.length > 0 && (
                <TouchableOpacity onPress={onClear}>
                    <View style={styles.clearButton}>
                        <Ionicons name="close" size={16} color="#1F1D2B" />
                    </View>
                </TouchableOpacity>
            )}
        </View>
    </View>
);

// A reusable component for sections with chips (History, Popular, etc.)
const ChipSection = ({ title, items, onChipPress, onClear }) => {
    if (!items || items.length === 0) return null;
    return (
        <View style={styles.section}>
            <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>{title}</Text>
                {onClear && (
                    <TouchableOpacity onPress={onClear}>
                        <Text style={styles.clearAllText}>clear all</Text>
                    </TouchableOpacity>
                )}
            </View>
            <View style={styles.chipContainer}>
                {items.map((term, index) => (
                    <TouchableOpacity key={`${term}-${index}`} style={styles.chip} onPress={() => onChipPress(term)}>
                        <Text style={styles.chipText}>{term}</Text>
                    </TouchableOpacity>
                ))}
            </View>
        </View>
    );
};


// Redesigned search result item
const SearchResultItem = React.memo(({ item, onNavigate }) => (
    <TouchableOpacity style={styles.resultItem} onPress={() => onNavigate(item)}>
        <Image
            source={{ uri: getCoverImageUrl(item.md_covers?.[0]) }}
            style={styles.resultImage}
            placeholder="blurhash|L07[g_j[fQfQ00fQfQfQ00j[fQj["
        />
        <View style={styles.resultInfo}>
            <Text style={styles.resultTitle} numberOfLines={2}>{item.title}</Text>
            <Text style={styles.resultAuthor} numberOfLines={1}>{item.authors?.[0] || 'Unknown Author'}</Text>
            <Text style={styles.resultDesc} numberOfLines={2}>{item.desc || 'No description provided.'}</Text>
        </View>
    </TouchableOpacity>
));

const EmptyState = ({ title, message }) => (
    <View style={styles.emptyStateContainer}>
        <Ionicons name="search-circle-outline" size={80} color="#2D2A41" />
        <Text style={styles.emptyStateTitle}>{title}</Text>
        <Text style={styles.emptyStateMessage}>{message}</Text>
    </View>
)


// --- Main SearchScreen Component ---
export default function SearchScreen({ navigation }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [searchHistory, setSearchHistory] = useState([]);
  const [popularSuggestions, setPopularSuggestions] = useState([]);

  // Animation value for fade-in effect
  const contentOpacity = useSharedValue(0);
  
  const loadInitialData = async () => {
    try {
        const [historyString, trendingData] = await Promise.all([
             AsyncStorage.getItem(SEARCH_HISTORY_KEY),
             comickAPI.getTrendingComics(),
        ]);

        if (historyString) setSearchHistory(JSON.parse(historyString));
        if (trendingData.rank) {
            // Use popular comic titles as search suggestions
            setPopularSuggestions(trendingData.rank.slice(0, 6).map(c => c.title));
        }
        contentOpacity.value = withTiming(1, { duration: 500 }); // Fade in content
    } catch(e) {
        console.error("Failed to load initial data", e)
    }
  };

  useFocusEffect(useCallback(() => { loadInitialData() }, []));

  const saveSearchHistory = async (term) => {
    try {
      const newHistory = [term, ...searchHistory.filter(t => t.toLowerCase() !== term.toLowerCase())].slice(0, 5);
      setSearchHistory(newHistory);
      await AsyncStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(newHistory));
    } catch (e) { console.error("Failed to save history.", e) }
  };

  const clearSearchHistory = async () => {
    try {
        setSearchHistory([]);
        await AsyncStorage.removeItem(SEARCH_HISTORY_KEY);
    } catch (e) { console.error("Failed to clear history.", e) }
  }

  const handleSearch = async (query) => {
    const term = query.trim();
    if (!term) return;

    setLoading(true); setHasSearched(true);
    try {
      const results = await comickAPI.searchComics(term);
      setSearchResults(results || []);
      saveSearchHistory(term);
    } catch (error) { Alert.alert('Error', 'Search failed. Please try again.'); } 
    finally { setLoading(false); }
  };
  
  const handleClear = () => {
    setSearchQuery(''); setSearchResults([]); setHasSearched(false);
  }

  const navigateToDetail = (comic) => {
    navigation.navigate('ManhwaDetail', { slug: comic.slug, hid: comic.hid, title: comic.title });
  };
  
  const PreSearchContent = () => (
      <Animated.ScrollView>
        <ChipSection
            title="Last search"
            items={searchHistory}
            onClear={clearSearchHistory}
            onChipPress={(term) => {
                setSearchQuery(term); handleSearch(term);
            }}
        />
        <ChipSection
            title="Popular Right Now"
            items={popularSuggestions}
            onChipPress={(term) => {
                setSearchQuery(term); handleSearch(term);
            }}
        />
      </Animated.ScrollView>
  );

  return (
    <SafeAreaView style={styles.container}>
        <View style={styles.header}>
            <SearchBar 
                query={searchQuery}
                onQueryChange={setSearchQuery}
                onClear={handleClear}
                onSubmit={() => handleSearch(searchQuery)}
                onBack={() => navigation.goBack()}
            />
        </View>
        
        {loading ? (
             <ActivityIndicator style={{marginTop: 50}} size="large" color="#FFF" />
        ) : hasSearched ? (
            <FlatList
                data={searchResults}
                renderItem={({item}) => <SearchResultItem item={item} onNavigate={navigateToDetail} />}
                keyExtractor={(item) => item.hid}
                ListEmptyComponent={
                    <EmptyState
                        title={`No results found`}
                        message={`Try searching for a different title or author.`}
                    />
                }
                contentContainerStyle={{paddingHorizontal: 20, paddingTop: 10}}
            />
        ) : (
            <PreSearchContent />
        )}
    </SafeAreaView>
  );
}

// --- Stylesheet Recreated From Image ---
const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#1F1D2B' },
  header: { paddingBottom: 10 },
  searchBarRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: 10
  },
  backButton: {
      padding: 5,
      marginRight: 10
  },
  searchBarContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2D2A41',
    borderRadius: 12,
    paddingHorizontal: 15,
  },
  searchInput: {
    flex: 1,
    height: 50,
    color: '#FFF',
    fontSize: 16,
    marginLeft: 10,
  },
  clearButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#8A8899',
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    marginTop: 25,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    color: '#FFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  clearAllText: {
    color: '#A39DCE',
    fontSize: 14,
    fontWeight: '600',
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  chip: {
    backgroundColor: 'rgba(45, 42, 65, 0.8)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 10,
    marginBottom: 10,
  },
  chipText: {
    color: '#D0CFD4',
    fontSize: 14,
    fontWeight: '500',
  },
  emptyStateContainer:{
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: '25%',
  },
  emptyStateTitle: {
    color: '#FFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 15,
  },
  emptyStateMessage: {
      color: '#8A8899',
      fontSize: 15,
      textAlign: 'center',
      marginTop: 8,
      paddingHorizontal: 20
  },
  // Search Result Item Styles
  resultItem: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  resultImage: {
    width: 80,
    height: 120,
    borderRadius: 8,
    backgroundColor: '#333'
  },
  resultInfo: {
    flex: 1,
    marginLeft: 15,
    justifyContent: 'center',
  },
  resultTitle: {
    color: '#FFF',
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 4,
  },
  resultAuthor: {
    color: '#A39DCE',
    fontSize: 14,
    marginBottom: 8,
  },
  resultDesc: {
    color: '#8A8899',
    fontSize: 13,
    lineHeight: 18,
  },
});