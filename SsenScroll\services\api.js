import axios from 'axios';

const BASE_URL = 'https://api.comick.fun';

// Create axios instance
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
  },
});

// API service functions
export const comickAPI = {
  // Get trending comics
  getTrendingComics: async (params = {}) => {
    try {
      const response = await api.get('/top', {
        params: {
          type: 'trending',
          comic_types: 'manhwa',
          accept_mature_content: false,
          ...params,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching trending comics:', error);
      throw error;
    }
  },

  // Get latest chapters
  getLatestChapters: async (params = {}) => {
    try {
      const response = await api.get('/chapter/', {
        params: {
          type: 'manhwa',
          page: 1,
          limit: 20,
          order: 'hot',
          ...params,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching latest chapters:', error);
      throw error;
    }
  },

  // Search comics
  searchComics: async (query, params = {}) => {
    try {
      const response = await api.get('/v1.0/search/', {
        params: {
          q: query,
          limit: 20,
          page: 1,
          ...params,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error searching comics:', error);
      throw error;
    }
  },

  // --- NEW SECTIONS ---
  // New function to get completed series
  getCompletedComics: async (params = {}) => {
    try {
      // Use the search endpoint with status=2 for completed and no 'q' parameter.
      const response = await api.get('/v1.0/search/', {
        params: {
          status: 2, // 2 = Completed
          comic_types: 'manhwa',
          limit: 15,
          ...params,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching completed comics:', error);
      throw error;
    }
  },

  // New function to get the highest-rated comics
  getHighestRatedComics: async (params = {}) => {
    try {
      // Use the search endpoint with sort='rating' and no 'q' parameter.
      const response = await api.get('/v1.0/search/', {
        params: {
          sort: 'rating', // Sort by rating
          comic_types: 'manhwa',
          limit: 15,
          ...params,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching highest rated comics:', error);
      throw error;
    }
  },
  // --- END OF NEW SECTIONS ---

  // Get comic details
  getComicDetails: async (slug) => {
    try {
      const response = await api.get(`/comic/${slug}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching comic details:', error);
      throw error;
    }
  },

  // Get comic chapters
  getComicChapters: async (hid, params = {}) => {
    try {
      const response = await api.get(`/comic/${hid}/chapters`, {
        params: {
          limit: 60,
          page: 0,
          ...params,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching comic chapters:', error);
      throw error;
    }
  },

  // Get chapter details
  getChapterDetails: async (hid) => {
    try {
      const response = await api.get(`/chapter/${hid}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching chapter details:', error);
      throw error;
    }
  },

  // Get chapter images
  getChapterImages: async (hid) => {
    try {
      const response = await api.get(`/chapter/${hid}/get_images`);
      return response.data;
    } catch (error) {
      console.error('Error fetching chapter images:', error);
      throw error;
    }
  },
};

// Helper function to get cover image URL
export const getCoverImageUrl = (cover) => {
  if (!cover || !cover.b2key) return null;
  return `https://meo.comick.pictures/${cover.b2key}`;
};

// Helper function to get chapter image URL
export const getChapterImageUrl = (image) => {
  if (!image || !image.b2key) return null;
  return `https://meo.comick.pictures/${image.b2key}`;
};

// Helper function to format genre names
export const formatGenres = (genres) => {
  // This would need a genre mapping, for now just return the IDs
  return genres ? genres.slice(0, 3).join(', ') : '';
};

// Helper function to format status
export const formatStatus = (status) => {
  const statusMap = {
    1: 'Ongoing',
    2: 'Completed',
    3: 'Cancelled',
    4: 'Hiatus',
  };
  return statusMap[status] || 'Unknown';
};

// Helper function to format content rating
export const formatContentRating = (rating) => {
  const ratingMap = {
    safe: 'Safe',
    suggestive: 'Suggestive',
    erotica: 'Erotica',
  };
  return ratingMap[rating] || 'Unknown';
};

// Helper function to group chapters by scan group and language
export const groupChaptersByScanlator = (chapters) => {
  const grouped = {};

  chapters.forEach(chapter => {
    const groupName = chapter.group_name?.[0] || 'Unknown';
    const lang = chapter.lang || 'en';
    const key = `${groupName}_${lang}`;

    if (!grouped[key]) {
      grouped[key] = {
        groupName,
        language: lang,
        chapters: []
      };
    }

    grouped[key].chapters.push(chapter);
  });

  // Sort chapters within each group by chapter number (descending)
  Object.values(grouped).forEach(group => {
    group.chapters.sort((a, b) => {
      const aChap = parseFloat(a.chap) || 0;
      const bChap = parseFloat(b.chap) || 0;
      return bChap - aChap;
    });
  });

  return grouped;
};

// Helper function to get language display name
export const getLanguageDisplayName = (langCode) => {
  const langMap = {
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'pt': 'Portuguese',
    'ru': 'Russian',
    'ja': 'Japanese',
    'ko': 'Korean',
    'zh': 'Chinese',
    'ar': 'Arabic',
    'th': 'Thai',
    'vi': 'Vietnamese',
    'pl': 'Polish',
    'tr': 'Turkish',
  };
  return langMap[langCode] || langCode.toUpperCase();
};
